<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-parent -->
	<parent>
		<groupId>com.hearyou</groupId>
		<artifactId>common-base</artifactId>
		<version>3.0</version>
		<relativePath/>
	</parent>

	<groupId>com.hearyou</groupId>
	<artifactId>anything-llm-api-manager</artifactId>
	<version>1.0.0</version>
	<name>anything-llm-api-manager</name>
	<description>LLM知识库管理系统</description>

	<properties>
		<java.version>17</java.version>
		<dubbo-spring.version>3.3.4</dubbo-spring.version>
		<mybatis-plus.version>3.5.12</mybatis-plus.version>

		<!--私服配置-->
		<nexus.url>http://************:18081/nexus</nexus.url>
		<artifactory.url>http://************:58081</artifactory.url>
		<!--<doc.type>html</doc.type>
		<doc.name>doc-html.json</doc.name>-->
		<doc.type>torna-rest</doc.type>
		<doc.name>doc-torna.json</doc.name>
		<smart-doc.version>3.1.1</smart-doc.version>
		<jib.version>3.4.5</jib.version>
		<jdk-image-name>openjdk:17-jdk-oracle</jdk-image-name>
		<build-image-name>************:18082/${project.name}:${project.version}</build-image-name>

		<!-- utils -->
		<!--<jakarta.version>3.1.1</jakarta.version>-->

		<!-- 组件 -->
		<sa-token.version>1.43.0</sa-token.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<!-- WebFlux -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webflux</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<!--信息监控-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!--<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>-->

		<!-- Dubbo集成 -->
		<!--<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
			<version>${dubbo-spring.version}</version>
		</dependency>-->

		<!-- 数据库层 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
			<version>${mybatis-plus.version}</version>
		</dependency>

		<!-- jdk 11+ 引入可选模块 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-jsqlparser</artifactId>
			<version>${mybatis-plus.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${mapstruct.version}</version>
		</dependency>

		<!--utils-->
		<dependency>
			<groupId>com.hearyou</groupId>
			<artifactId>common-utils</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<dependency>
			<groupId>com.hearyou</groupId>
			<artifactId>common-base-business</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<dependency>
			<groupId>com.hearyou</groupId>
			<artifactId>common-minio-client-spring-boot-starter</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<!-- 认证 -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-spring-boot3-starter</artifactId>
			<version>${sa-token.version}</version>
		</dependency>

		<!-- Sa-Token 整合 RedisTemplate -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-redis-template</artifactId>
			<version>${sa-token.version}</version>
		</dependency>

		<!-- 提供 Redis 连接池 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5-fluent</artifactId>
		</dependency>

		<!--多级缓存-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>

		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<!--实例转换插件，激活Idea插件 + Lombok自动生成 + mapstruct自动生成插件-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>

			<!--打包的时候顺便打上源码包-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>com.ly.smart-doc</groupId>
				<artifactId>smart-doc-maven-plugin</artifactId>
				<version>${smart-doc.version}</version>
				<configuration>
					<configFile>${basedir}/src/main/resources/${doc.name}</configFile>
					<includes>
						<include>com.hearyou:*</include>
					</includes>
				</configuration>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>${doc.type}</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<!--谷歌打包java程序为docker的插件-->
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>jib-maven-plugin</artifactId>
				<version>${jib.version}</version>
				<configuration>
					<from>
						<image>${jdk-image-name}</image>
					</from>
					<to>
						<image>${build-image-name}</image>
						<!--<auth>
                            <username>docker_admin</username>
                            <password>hearyou2021</password>
                        </auth>-->
					</to>
					<container>
						<!--挂载volume的配置-->
						<volumes>
							<volume>/logs</volume>
						</volumes>
						<creationTime>USE_CURRENT_TIMESTAMP</creationTime>
					</container>
					<!--允许非https-->
					<allowInsecureRegistries>true</allowInsecureRegistries>
				</configuration>
				<executions>
					<execution>
						<id>build-and-push-docker-image</id>
						<phase>package</phase>
						<goals>
							<goal>build</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
