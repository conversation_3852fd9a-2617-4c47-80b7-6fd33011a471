# anything-llm-api-manager（知识库管理系统）

## 打包脚本
> mvn clean package jib:build -s D:/devTools/apache-maven-3.6.3-bin/settings-hearyou.xml -DsendCredentialsOverHttp=true -Djib.to.auth.username=docker_admin -Djib.to.auth.password=hearyou2021 -Djib.from.image=tar://D:/base_image/17-jdk-oracle.tar

## llm系统信息
http://hykj.derom.com:3001/
hearyou  /  hearyou2025

AI接口访问地址:
http://hykj.derom.com:11889

LLM文档地址：
http://hykj.derom.com:3001/api/docs/#

服务器拉取最新镜像命令:
```shell
docker pull 120.26.98.46:9000/anything-llm-api-manager:1.0.0
```

