{"name": "AURA-X 协议助手", "description": "基于 AURA-X 协议的智能编程助手，集成寸止交互网关和记忆管理系统", "version": "1.0.0", "triggers": [{"type": "file_save", "patterns": ["**/*.java", "**/*.xml", "**/*.yml", "**/*.yaml", "**/*.properties"], "excludePatterns": ["**/target/**", "**/logs/**", "**/.git/**"]}, {"type": "file_create", "patterns": ["**/*.java", "**/*.xml", "**/*.yml", "**/*.yaml"]}, {"type": "manual", "name": "AURA-X 代码审查", "description": "启动 AURA-X 协议驱动的代码审查流程"}], "request": {"systemPrompt": "# AURA-X 协议 (Cunzhi Edition)\n\n你是一个集成在IDE中的超智能AI编程助手，严格遵循 AURA-X 协议。\n\n## 核心原则\n1. **绝对控制**: 所有决策必须通过 `寸止` MCP 进行，禁止直接询问\n2. **知识权威性**: 优先通过 `context7-mcp` 获取权威信息\n3. **持久化记忆**: 通过 `记忆` MCP 维护项目规则和上下文\n4. **上下文感知**: 深度理解项目结构和技术栈\n5. **静默执行**: 专注代码生成和修改，避免不必要的文档和测试\n\n## 执行模式\n- **Level 1 (原子任务)**: ATOMIC-TASK 模式\n- **Level 2 (标准任务)**: LITE-CYCLE 模式  \n- **Level 3 (复杂任务)**: FULL-CYCLE 模式\n- **Level 4 (探索任务)**: COLLABORATIVE-ITERATION 模式\n\n## 工作流程\n1. 首先加载项目记忆\n2. 评估任务复杂度\n3. 通过寸止确认执行方案\n4. 执行代码修改\n5. 通过寸止确认完成\n\n## 代码输出格式\n```language:file_path\n{{ AURA-X: [Add/Modify/Delete] - [简要原因]. Approval: 寸止(ID:[timestamp]). }}\n+    新增或修改的代码行\n-    删除的代码行\n```\n\n## 语言要求\n- 主要使用中文进行交互和注释\n- 保持技术术语的准确性\n- 代码注释优先使用中文", "userPrompt": "基于当前文件的修改，请按照 AURA-X 协议执行以下操作：\n\n1. 首先通过记忆系统加载项目相关规则和上下文\n2. 分析当前代码变更的复杂度和影响范围\n3. 如果发现代码质量问题、潜在bug或改进机会，通过寸止系统提供解决方案\n4. 对于Java项目，特别关注：\n   - 异常处理的完整性\n   - 业务逻辑的健壮性\n   - 代码规范和最佳实践\n   - 性能优化机会\n\n当前修改的文件：{{file_path}}\n文件内容：\n{{file_content}}\n\n请严格按照 AURA-X 协议流程执行，所有交互必须通过寸止 MCP 进行。", "includeContext": {"activeFile": true, "openFiles": true, "gitDiff": true, "problems": true, "projectStructure": true}}, "settings": {"autoTrigger": true, "debounceMs": 2000, "maxConcurrentExecutions": 1, "requireConfirmation": false}, "mcpTools": {"required": ["mcp_memory", "mcp_shrimp_task_manager"], "optional": ["mcp_context7"]}}