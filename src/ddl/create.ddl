CREATE database llm_manager  DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

use mysql;
CREATE USER 'llm_manager_admin'@'%' identified BY 'hearyoulma2025';
GRANT ALL ON llm_manager.* TO 'llm_manager_admin'@'%';

flush privileges;

use llm_manager;

DROP TABLE IF EXISTS user_info;
CREATE TABLE user_info(
                          id BIGINT(20) NOT NULL   COMMENT '' ,
                          user_name VARCHAR(32)   DEFAULT '' COMMENT '用户名' ,
                          password VARCHAR(256)   DEFAULT '' COMMENT '密码' ,
                          name VARCHAR(32)   DEFAULT '' COMMENT '姓名' ,
                          dept_code VARCHAR(32)   DEFAULT '' COMMENT '部门编码' ,
                          id_card VARCHAR(32)   DEFAULT '' COMMENT '身份证号' ,
                          age INT(10)   DEFAULT 1 COMMENT '年龄' ,
                          phone_num VARCHAR(32)   DEFAULT '' COMMENT '手机号' ,
                          address VARCHAR(64)   DEFAULT '' COMMENT '住址' ,
                          sex INT(10)   DEFAULT 0 COMMENT '性别;0-未知 1-男性 2-女性' ,
                          email_account VARCHAR(32)   DEFAULT '' COMMENT '邮箱账号' ,
                          nick_name VARCHAR(32)   DEFAULT '' COMMENT '昵称' ,
                          head_img VARCHAR(256)   DEFAULT '' COMMENT '头像url' ,
                          register_time DATETIME    COMMENT '注册账号时间' ,
                          register_ip VARCHAR(32)   DEFAULT '' COMMENT '注册时的IP' ,
                          register_approver VARCHAR(32)   DEFAULT '' COMMENT '注册审批人' ,
                          last_login_time DATETIME    COMMENT '上次登录时间' ,
                          llm_user_id VARCHAR(32)   DEFAULT '' COMMENT 'Llm系统的用户id' ,
                          user_create_type INT(10)   DEFAULT 0 COMMENT '用户名创建方式;0-管理员/系统创建 1-用户注册 2-外部系统导入 3-其他' ,
                          status INT(10)   DEFAULT 0 COMMENT '状态;0-激活 1-注册待审核 2-审核未通过 3-禁用 4-注销 5-其他' ,
                          version INT(10)   DEFAULT 0 COMMENT '版本号' ,
                          del_flag INT(10)   DEFAULT 0 COMMENT '删除标记;0-未删除 1-已删除' ,
                          create_by VARCHAR(32)   DEFAULT '' COMMENT '创建人' ,
                          create_time DATETIME   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                          update_by VARCHAR(32)   DEFAULT '' COMMENT '更新人' ,
                          update_time DATETIME   DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '更新时间' ,
                          tenant_code VARCHAR(32)   DEFAULT '' COMMENT '租户编码' ,
                          PRIMARY KEY (id)
)  COMMENT = '用户信息表';

DROP TABLE IF EXISTS dept;
CREATE TABLE dept(
                     id BIGINT(20) NOT NULL   COMMENT '' ,
                     dept_code VARCHAR(32)   DEFAULT '' COMMENT '科室编码' ,
                     dept_name VARCHAR(64)   DEFAULT '' COMMENT '科室名称' ,
                     parent_code VARCHAR(32)   DEFAULT '' COMMENT '上级科室编码' ,
                     version INT(10)   DEFAULT 0 COMMENT '版本号' ,
                     del_flag INT(10)   DEFAULT 0 COMMENT '删除标记;0-未删除 1-已删除' ,
                     create_by VARCHAR(32)   DEFAULT '' COMMENT '创建人' ,
                     create_time DATETIME   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                     update_by VARCHAR(32)   DEFAULT '' COMMENT '更新人' ,
                     update_time DATETIME   DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '更新时间' ,
                     tenant_code VARCHAR(32)   DEFAULT '' COMMENT '租户编码' ,
                     PRIMARY KEY (id)
)  COMMENT = '部门表';

DROP TABLE IF EXISTS sys_role;
CREATE TABLE sys_role(
                         id BIGINT(20) NOT NULL   COMMENT '' ,
                         role_code VARCHAR(32)   DEFAULT '' COMMENT '角色编码' ,
                         role_name VARCHAR(64)   DEFAULT '' COMMENT '角色名' ,
                         remark VARCHAR(256)   DEFAULT '' COMMENT '备注' ,
                         version INT(10)   DEFAULT 0 COMMENT '版本号' ,
                         del_flag INT(10)   DEFAULT 0 COMMENT '删除标记;0-未删除 1-已删除' ,
                         create_by VARCHAR(32)   DEFAULT '' COMMENT '创建人' ,
                         create_time DATETIME   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                         update_by VARCHAR(32)   DEFAULT '' COMMENT '更新人' ,
                         update_time DATETIME   DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '更新时间' ,
                         tenant_code VARCHAR(32)   DEFAULT '' COMMENT '租户编码' ,
                         PRIMARY KEY (id)
)  COMMENT = '角色表';

DROP TABLE IF EXISTS sys_permission;
CREATE TABLE sys_permission(
                               id BIGINT(20) NOT NULL   COMMENT '' ,
                               permission_code VARCHAR(32)   DEFAULT '' COMMENT '权限编码' ,
                               permission_name VARCHAR(64)   DEFAULT '' COMMENT '权限名' ,
                               permission_type INT(10)   DEFAULT 0 COMMENT '权限类型;0-菜单 1-按钮 2-接口API' ,
                               remark VARCHAR(256)   DEFAULT '' COMMENT '描述' ,
                               version INT(10)   DEFAULT 0 COMMENT '版本号' ,
                               del_flag INT(10)   DEFAULT 0 COMMENT '删除标记;0-未删除 1-已删除' ,
                               create_by VARCHAR(32)   DEFAULT '' COMMENT '创建人' ,
                               create_time DATETIME   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                               update_by VARCHAR(32)   DEFAULT '' COMMENT '更新人' ,
                               update_time DATETIME   DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '更新时间' ,
                               tenant_code VARCHAR(32)   DEFAULT '' COMMENT '租户编码' ,
                               PRIMARY KEY (id)
)  COMMENT = '权限表';

DROP TABLE IF EXISTS sys_user_role;
CREATE TABLE sys_user_role(
                              id BIGINT(20) NOT NULL   COMMENT '' ,
                              link_code VARCHAR(32)   DEFAULT '' COMMENT '关联编码' ,
                              user_name VARCHAR(32)   DEFAULT '' COMMENT '用户名' ,
                              role_code VARCHAR(32)   DEFAULT '' COMMENT '角色编码' ,
                              version INT(10)   DEFAULT 0 COMMENT '版本号' ,
                              del_flag INT(10)   DEFAULT 0 COMMENT '删除标记;0-未删除 1-已删除' ,
                              create_by VARCHAR(32)   DEFAULT '' COMMENT '创建人' ,
                              update_by VARCHAR(32)   DEFAULT '' COMMENT '更新人' ,
                              create_time DATETIME   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                              update_time DATETIME   DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '更新时间' ,
                              tenant_code VARCHAR(32)   DEFAULT '' COMMENT '租户编码' ,
                              PRIMARY KEY (id)
)  COMMENT = '用户角色表';

DROP TABLE IF EXISTS sys_role_permission;
CREATE TABLE sys_role_permission(
                                    id BIGINT(20) NOT NULL   COMMENT '' ,
                                    link_code VARCHAR(32)   DEFAULT '' COMMENT '关联编码' ,
                                    role_code VARCHAR(32)   DEFAULT '' COMMENT '角色编码' ,
                                    permission_code VARCHAR(32)   DEFAULT '' COMMENT '权限编码' ,
                                    version INT(10)   DEFAULT 0 COMMENT '版本号' ,
                                    del_flag INT(10)   DEFAULT 0 COMMENT '删除标记;0-未删除 1-已删除' ,
                                    create_by VARCHAR(32)   DEFAULT '' COMMENT '创建人' ,
                                    update_by VARCHAR(32)   DEFAULT '' COMMENT '更新人' ,
                                    create_time DATETIME   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                    update_time DATETIME   DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                    tenant_code VARCHAR(32)   DEFAULT '' COMMENT '租户编码' ,
                                    PRIMARY KEY (id)
)  COMMENT = '角色权限表';

#初始化数据--创建一个admin角色
