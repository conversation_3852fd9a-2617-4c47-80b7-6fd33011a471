package com.hearyou.anything.manager.vo.response.llm.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Date;

/**
 * llm 用户信息返回对象
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/3 16:52
 */
@Data
public class LlmUserResponseVO {

	/**
	 * 用户ID
	 * @mock 1
	 */
	@JsonProperty("id")
	private Integer id;

	/**
	 * 用户名
	 * @mock admin
	 */
	@JsonProperty("username")
	private String userName;

	/**
	 * 用户头像文件的文件名
	 * @mock admin.jpg
	 */
	@JsonProperty("pfpFilename")
	private String pfpFilename;

	/**
	 * 用户角色名
	 * @mock admin
	 */
	@JsonProperty("role")
	private String role;

	/**
	 * 用户账户的挂起状态 0：未挂起 1：临时禁用
	 * @mock 0
	 */
	@JsonProperty("suspended")
	private Integer suspended;

	/**
	 * 用户是否已查看过账户恢复码
	 * @mock false
	 */
	@JsonProperty("seen_recovery_codes")
	private Boolean seenRecoveryCodes;

	/**
	 * 用户创建时间
	 * @mock 2025-06-03 16:52:00
	 */
	@JsonProperty("createdAt")
	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "UTC")
	private Date createdAt;

	/**
	 * 用户最后一次更新时间
	 * @mock 2025-06-03 16:52:00
	 */
	@JsonProperty("lastUpdatedAt")
	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "UTC")
	private Date lastUpdatedAt;

	/**
	 * 用户每日消息发送频率限制
	 * @mock 100
	 */
	@JsonProperty("dailyMessageLimit")
	private Integer dailyMessageLimit;

	/**
	 * 用户个人简介
	 * @mock 我是管理员
	 */
	@JsonProperty("bio")
	private String bio;
}