package com.hearyou.anything.manager.vo.response.document;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class DocumentResultResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 操作是否成功的标志
     * @mock true
     */
    private boolean success;

    /**
     * 错误信息，可为空。使用Object类型以获得最大灵活性。
     * @mock null
     */
    private Object error;

    /**
     * 文档实体对象列表
     */
    private List<DocumentResponseVO> documents;


}
