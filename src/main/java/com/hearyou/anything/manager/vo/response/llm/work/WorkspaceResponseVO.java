package com.hearyou.anything.manager.vo.response.llm.work;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 工作区信息
 */
@Data
public class WorkspaceResponseVO {

    /**
     * 工作区ID
     * @mock 1
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 工作区名称
     * @mock "hearyou"
     */
    @JsonProperty("name")
    private String name;

    /**
     * 工作区标识符
     * @mock "hearyou", "4f1e55b9-b3e0-43bf-8e9d-60b87e582698"
     */
    @JsonProperty("slug")
    private String slug;

    /**
     * 向量标签
     * @mock null
     */
    @JsonProperty("vectorTag")
    private String vectorTag;

    /**
     * 创建时间 (UTC)
     * @mock "2025-04-10T07:02:42.716Z"
     */
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "UTC")
    private Date createdAt;

    /**
     * OpenAI温度系数
     * @mock 0.7
     */
    @JsonProperty("openAiTemp")
    private Double openAiTemp;

    /**
     * OpenAI历史记录长度
     * @mock 20
     */
    @JsonProperty("openAiHistory")
    private Integer openAiHistory;

    /**
     * 最后更新时间 (UTC)
     * @mock "2025-04-10T07:02:42.716Z"
     */
    @JsonProperty("lastUpdatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "UTC")
    private Date lastUpdatedAt;

    /**
     * OpenAI提示词模板
     * @mock "Given the following conversation..."
     */
    @JsonProperty("openAiPrompt")
    private String openAiPrompt;

    /**
     * 相似度阈值
     * @mock 0.25
     */
    @JsonProperty("similarityThreshold")
    private Double similarityThreshold;

    /**
     * 聊天服务提供商
     * @mock "ollama"
     */
    @JsonProperty("chatProvider")
    private String chatProvider;

    /**
     * 聊天模型名称
     * @mock "deepseek-r1:32b"
     */
    @JsonProperty("chatModel")
    private String chatModel;

    /**
     * 顶部结果数量
     * @mock 4
     */
    @JsonProperty("topN")
    private Integer topN;

    /**
     * 聊天模式
     * @mock "chat"
     */
    @JsonProperty("chatMode")
    private String chatMode;

    /**
     * 头像文件名
     * @mock null
     */
    @JsonProperty("pfpFilename")
    private String pfpFilename;

    /**
     * 代理服务提供商
     * @mock "ollama"
     */
    @JsonProperty("agentProvider")
    private String agentProvider;

    /**
     * 代理模型名称
     * @mock "deepseek-r1:32b"
     */
    @JsonProperty("agentModel")
    private String agentModel;

    /**
     * 查询拒绝响应
     * @mock "There is no relevant information..."
     */
    @JsonProperty("queryRefusalResponse")
    private String queryRefusalResponse;

    /**
     * 向量搜索模式
     * @mock "default"
     */
    @JsonProperty("vectorSearchMode")
    private String vectorSearchMode;

    /**
     * 对话线程列表
     */
    @JsonProperty("threads")
    private List<ThreadItemResponseVO> threads;
}

