package com.hearyou.anything.manager.vo.response.llm.thread;

import lombok.Data;

import java.io.Serializable;
import java.time.OffsetDateTime;

@Data
public class ThreadResponseVO implements Serializable {


    private static final long serialVersionUID = 1L;


    /**
     * 线程的唯一ID
     * @mock 24
     */
    private Integer id;

    /**
     * 线程的显示名称
     * @mock "测试"
     */
    private String name;

    /**
     * 线程的唯一标识符 (slug)
     * @mock "11dd573a-f845-4de5-b265-6c7db4a4e66f"
     */
    private String slug;

    /**
     * 关联的工作空间ID
     * @mock 1
     */
    private Integer workspaceId;

    /**
     * 关联的用户ID，可以为空
     * @mock null
     */
    private Integer userId;


}
