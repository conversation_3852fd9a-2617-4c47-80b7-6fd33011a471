package com.hearyou.anything.manager.vo.response.llm.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 工作区绑定的用户信息 响应VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/12 19:43
 */
@Data
public class LlmWorkBindUserResponseVO {

    /**
     * 用户唯一标识符
     * 系统自动生成的数字ID
     */
    @JsonProperty("userId")
    private Long userId;

    /**
     * 用户登录账号名
     * 用于系统登录的身份凭证
     */
    @JsonProperty("username")
    private String username;

    /**
     * 用户角色标识
     * 定义用户访问权限等级
     * 示例值: admin/default/guest
     */
    @JsonProperty("role")
    private String role;
}