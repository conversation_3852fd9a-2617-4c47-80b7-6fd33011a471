package com.hearyou.anything.manager.vo.response.llm.work;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hearyou.anything.manager.vo.response.llm.thread.LlmWorkSpaceThreadResponseVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class LlmWorkSpaceResponseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工作区编码
     **/
    private String workspaceCode;

    /**
     * 工作区名称
     **/
    private String workspaceName;

    /**
     * 工作区slug
     **/
    private String workspaceSlug;

    /**
     * 是否启用;0-false 1-true
     **/
    private Integer whetherEnable;


    /**
     * 工作区下的线程
     */
    private List<LlmWorkSpaceThreadResponseVO> threadList = new ArrayList<>();
}
