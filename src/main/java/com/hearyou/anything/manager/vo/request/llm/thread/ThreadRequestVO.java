package com.hearyou.anything.manager.vo.request.llm.thread;

import lombok.Data;

import java.io.Serializable;

@Data
public class ThreadRequestVO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 所属用户的唯一ID
     * @mock 1
     */
    private Integer userId;

    /**
     * 线程的显示名称
     * @mock "Name"
     */
    private String name;

    /**
     * 线程的唯一标识符 (slug)
     * @mock "thread-slug"
     */
    private String slug;
}
