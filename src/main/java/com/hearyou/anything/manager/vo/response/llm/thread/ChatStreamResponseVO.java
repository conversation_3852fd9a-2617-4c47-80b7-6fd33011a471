package com.hearyou.anything.manager.vo.response.llm.thread;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 流式对话响应VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/9 15:30
 */
@Data
public class ChatStreamResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符
     * @mock "2db21982-34dd-4ac7-bf95-6c2a1f5f34f9"
     */
    @JsonProperty("uuid")
    private String uuid;

    /**
     * 引用来源列表
     */
    @JsonProperty("sources")
    private List<SourceResponseVO> sources;

    /**
     * 响应类型
     * @mock "textResponseChunk"
     */
    @JsonProperty("type")
    private String type;

    /**
     * 文本响应内容
     * @mock "你好"
     */
    @JsonProperty("textResponse")
    private String textResponse;

    /**
     * 是否关闭连接
     * @mock false
     */
    @JsonProperty("close")
    private Boolean close;

    /**
     * 是否有错误
     * @mock false
     */
    @JsonProperty("error")
    private Boolean error;
}
