package com.hearyou.anything.manager.vo.request.business;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 登录参数 请求VO实体
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/11 17:27
 */
@Data
public class LoginRequestVO {

    /**
     * 用户名
     * @mock admin
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     * @mock 123456
     */
    @NotBlank(message = "密码不能为空")
    private String password;
}
