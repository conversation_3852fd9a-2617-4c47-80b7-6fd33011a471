package com.hearyou.anything.manager.vo.response.llm.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hearyou.anything.manager.vo.response.llm.LlmBaseResultResponseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 工作区绑定用户集合 结果 响应VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/12 19:44
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LlmWorkBindUsersResultResponseVO extends LlmBaseResultResponseVO {
    
    /**
     * 当前工作区绑定的用户基础信息列表
     */
    @JsonProperty("users")
    private List<LlmWorkBindUserResponseVO> users;

}