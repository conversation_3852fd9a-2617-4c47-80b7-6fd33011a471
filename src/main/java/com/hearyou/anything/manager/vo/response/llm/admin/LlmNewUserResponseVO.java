package com.hearyou.anything.manager.vo.response.llm.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * llm创建用户响应对象
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/5 18:20
 */
@Data
public class LlmNewUserResponseVO {

    /**
     * 用户id
     * @mock 1
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 用户名
     * @mock test_manager
     */
    @JsonProperty("username")
    private String username;

    /**
     * 角色名组合
     * @mock default
     */
    @JsonProperty("role")
    private String role;
}
