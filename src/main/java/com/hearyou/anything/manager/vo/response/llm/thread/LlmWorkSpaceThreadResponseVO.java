package com.hearyou.anything.manager.vo.response.llm.thread;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作区线程响应VO
 */
@Data
public class LlmWorkSpaceThreadResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     * @mock 1
     */
    private Long id;

    /**
     * 工作区线程编码(系统业务编码)
     * @mock "WT123456789"
     */
    private String workspaceThreadCode;

    /**
     * 工作区线程名称
     * @mock "测试线程"
     */
    private String workspaceThreadName;

    /**
     * 工作区线程slug
     * @mock "thread-slug-123"
     */
    private String workspaceThreadSlug;

    /**
     * 绑定的用户名
     * @mock "admin"
     */
    private String userName;

    /**
     * 工作区slug
     * @mock "workspace-slug-456"
     */
    private String workspaceSlug;

    /**
     * 创建时间
     * @mock "2025-07-29 10:30:00"
     */
    private Date createTime;

    /**
     * 更新时间
     * @mock "2025-07-29 15:45:00"
     */
    private Date updateTime;
}
