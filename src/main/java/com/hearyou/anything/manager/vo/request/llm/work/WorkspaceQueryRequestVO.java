package com.hearyou.anything.manager.vo.request.llm.work;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 工作区查询条件请求VO
 */
@Data
public class WorkspaceQueryRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作区名称
     * 支持模糊查询
     * @mock "工作区1"
     */
    private String workspaceName;

    /**
     * 工作区slug集合
     * 支持多个slug的IN查询
     * @mock ["hearyou", "4f1e55b9-b3e0-43bf-8e9d-60b87e582698"]
     */
    private List<String> workspaceSlugs;

    /**
     * 是否启用状态
     * 0-禁用 1-启用
     * @mock 1
     */
    private Integer whetherEnable;
}