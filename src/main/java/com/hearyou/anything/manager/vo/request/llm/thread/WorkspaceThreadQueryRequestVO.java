package com.hearyou.anything.manager.vo.request.llm.thread;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 工作区线程查询条件请求VO
 */
@Data
public class WorkspaceThreadQueryRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作区线程名称
     * @mock "测试线程"
     */
    private String workspaceThreadName;

    /**
     * 工作区线程slug集合
     * @mock ["thread-slug-1", "thread-slug-2"]
     */
    private List<String> workspaceThreadSlugs;

    /**
     * 绑定的用户名
     * @mock "admin"
     */
    private String userName;

    /**
     * 工作区slug
     * @mock "workspace-slug-456"
     */
    private String workspaceSlug;

    /**
     * 工作区slug集合
     * @mock ["workspace-1", "workspace-2"]
     */
    private List<String> workspaceSlugs;
}
