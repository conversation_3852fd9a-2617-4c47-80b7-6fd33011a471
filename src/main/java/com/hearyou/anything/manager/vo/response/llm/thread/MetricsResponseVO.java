package com.hearyou.anything.manager.vo.response.llm.thread;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class MetricsResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * @mock 54
     */
    @JsonProperty("prompt_tokens")
    private Integer promptTokens;

    /**
     * @mock 326
     */
    @JsonProperty("completion_tokens")
    private Integer completionTokens;

    /**
     * @mock 380
     */
    @JsonProperty("total_tokens")
    private Integer totalTokens;

    /**
     * @mock 36.038027857616626
     */
    @JsonProperty("outputTps")
    private Double outputTps;

    /**
     * @mock 9.046
     */
    @JsonProperty("duration")
    private Double duration;


}
