package com.hearyou.anything.manager.vo.response.llm.work;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 对话线程项
 */
@Data
public class ThreadItemResponseVO {

    /**
     * 用户ID
     * @mock 1
     */
    @JsonProperty("user_id")
    private Integer userId;

    /**
     * 线程标识符
     * @mock "a6b11cbd-83aa-4df0-b622-52b8ba9a0fbd"
     */
    @JsonProperty("slug")
    private String slug;

    /**
     * 线程名称
     * @mock "Thread", "python程序判断自身程序的后缀名..."
     */
    @JsonProperty("name")
    private String name;
}