package com.hearyou.anything.manager.vo.request.llm.work;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddWorkspaceRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作空间名称
     * @mock "My New Workspace"
     */
    @NotBlank(message = "工作空间名称不能为空")
    private String name;


    /**
     * 工作区标识符
     * @mock "hearyou", "4f1e55b9-b3e0-43bf-8e9d-60b87e582698"
     */
    private String workspaceSlug;

    /**
     * 相似度阈值
     * 用于向量搜索或内容推荐
     * @mock 0.7
     */
    private Double similarityThreshold;

    /**
     * OpenAI 温度系数
     * @mock 0.7
     */
    private Double openAiTemp;

    /**
     * OpenAI 历史记录长度
     * @mock 20
     */
    private Integer openAiHistory;

    /**
     * OpenAI 提示词模板
     * 自定义的系统级提示，用于指导AI模型的响应。
     * @mock "Custom prompt for responses"
     */
    private String openAiPrompt;

    /**
     * 查询拒绝响应
     * 当无法找到相关信息或查询被拒绝时，系统返回的自定义消息。
     * @mock "Custom refusal message"
     */
    private String queryRefusalResponse;

    /**
     * 聊天模式
     * 定义工作空间的交互模式，例如 'chat' 或 'query'。
     * @mock "chat"
     */
    private String chatMode;

    /**
     * 顶部结果数量
     * 在查询或搜索中返回的最相关结果的数量，为整数，因此使用 Integer。
     * @mock 4
     */
    private Integer topN;
}
