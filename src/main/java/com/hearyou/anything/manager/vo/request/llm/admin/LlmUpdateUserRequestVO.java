package com.hearyou.anything.manager.vo.request.llm.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Llm修改用户请求VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/7 19:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LlmUpdateUserRequestVO extends LlmNewUserRequestVO{

    /**
     * 账户状态：0（启用）或 1（禁用）
     * @mock 0
     */
    @JsonProperty("suspended")
    private Integer suspended;
}
