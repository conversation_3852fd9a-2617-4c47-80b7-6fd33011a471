package com.hearyou.anything.manager.vo.response.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class DocumentResponseVO  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文档的唯一ID
     * @mock "b4d1c739-dedd-4b67-b535-b8bc0a43cdf1"
     */
    private String id;

    /**
     * 文档的URL或本地文件路径
     * @mock
     */
    private String url;

    /**
     * 文档标题
     * @mock "mysql索引.pdf"
     */
    private String title;

    /**
     * 文档作者
     * @mock
     */
    private String docAuthor;

    /**
     * 描述信息
     * @mock
     */
    private String description;

    /**
     * 文档来源说明
     * @mock
     */
    private String docSource;

    /**
     * 数据块来源
     * @mock ""
     */
    private String chunkSource;

    /**
     * 发布日期或时间
     * @mock
     */
    private String published;

    /**
     * 词数统计
     * @mock 307
     */
    private Integer wordCount;

    /**
     * 页面内容
     * @mock
     */
    private String pageContent;

    /**
     * 预估的Token数量。
     * @mock 1888
     */
    @JsonProperty("token_count_estimate")
    private Integer tokenCountEstimate;

    /**
     * 文件位置
     * @mock "custom-documents/mysql.pdf-b4d1c739-dedd-4b67-b535-b8bc0a43cdf1.json"
     */
    private String location;
}
