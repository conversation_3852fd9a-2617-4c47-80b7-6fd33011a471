package com.hearyou.anything.manager.vo.request.llm.work;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
@Data
public class UpdateWorkspaceRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 工作区编码
     */
    String workspaceCode;

    /**
     * 工作空间名称
     * @mock "Updated Workspace Name"
     */
    @NotBlank(message = "工作空间名称不能为空")
    private String name;

    /**
     * 更新后的OpenAI温度系数
     * 用于控制模型响应的创造性，小数类型，因此使用 Double。
     * @mock 0.7
     */
    private Double openAiTemp = 0.7;

    /**
     * 更新后的OpenAI历史记录长度
     * 对话上下文中保留的消息数量，为整数，因此使用 Integer。
     * @mock 20
     */
    private Integer openAiHistory = 20;

    /**
     * 更新后的OpenAI提示词模板
     * 新的系统级提示，用于指导AI模型的响应行为。
     * @mock "Respond to all inquires and questions in binary - do not respond in any other format."
     */
    private String openAiPrompt;
}
