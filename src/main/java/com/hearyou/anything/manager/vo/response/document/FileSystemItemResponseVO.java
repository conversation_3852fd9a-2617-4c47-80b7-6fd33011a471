package com.hearyou.anything.manager.vo.response.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FileSystemItemResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    // --- 文件和文件夹共有的字段 ---
    /**
     * 文件或文件夹的名称。
     * @mock "documents"
     */
    private String name;

    /**
     * 条目的类型，"folder" 或 "file"。
     * @mock "folder"
     */
    private String type;

    // --- 文件夹特有的字段 ---
    /**
     * 子目录
     */
    private List<FileSystemItemResponseVO> items;

    // --- 文件特有的字段 ---
    /**
     * 文件的唯一ID。
     * @mock "bd4fb193-b348-4667-b496-20dbe5d097e5"
     */
    private String id;

    /**
     * 文件的URL或路径。
     * @mock
     */
    private String url;

    /**
     * 文档的标题。
     * @mock "Apifox文档.pdf"
     */
    private String title;

    private String docAuthor;

    private String description;

    private String docSource;

    private String chunkSource;

    private String published;

    private Integer wordCount;

    /**
     * 预估的Token数量。
     * @mock 1866
     */
    @JsonProperty("token_count_estimate")
    private Integer tokenCountEstimate;

    private boolean cached;

    private boolean canWatch;

    private List<Object> pinnedWorkspaces;

    private boolean watched;
}
