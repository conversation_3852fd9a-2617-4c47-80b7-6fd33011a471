package com.hearyou.anything.manager.vo.request.llm.document;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 批量文件操作请求VO
 */
@Data
public class BatchFileOperationRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件编码列表
     * @mock ["F123456789", "F987654321"]
     */
    @NotEmpty(message = "文件编码列表不能为空")
    private List<String> fileCodes;

    /**
     * 工作区slug（用于批量状态变更时的LLM文档更新）
     * @mock "workspace-slug-123"
     */
    private String workspaceSlug;
}
