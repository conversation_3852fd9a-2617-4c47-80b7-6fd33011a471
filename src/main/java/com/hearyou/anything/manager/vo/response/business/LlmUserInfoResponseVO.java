package com.hearyou.anything.manager.vo.response.business;


import com.hearyou.common.base.BaseResponseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.List;

/**
 * LLM用户信息响应对象
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/21 18:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LlmUserInfoResponseVO extends BaseResponseVO {

    /**
     * 用户名
     * @mock admin
     */
    private String userName;

    /**
     * 用户角色编码
     * @mock admin
     */
    private String roleCode;

    /**
     * 用户角色明
     * @mock 超级管理员
     */
    private String roleName;

    /**
     * 用户工作区slug编码列表
     * @mock ["group1","group2"]
     */
    private List<String> workSlugList;

    /**
     * 用户工作区名称列表
     * @mock ["工作区1","工作区2"]
     */
    private List<String> workNameList;
}
