package com.hearyou.anything.manager.vo.response.llm.thread;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class ChatHistoryResponseVO implements Serializable {


    private static final long serialVersionUID = 1L;


    /**
     * 包含所有对话消息的历史记录列表
     */
    @JsonProperty("history")
    private List<ChatContentResponseVO> history;
}
