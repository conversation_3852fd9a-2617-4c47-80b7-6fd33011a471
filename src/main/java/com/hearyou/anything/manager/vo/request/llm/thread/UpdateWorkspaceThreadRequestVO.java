package com.hearyou.anything.manager.vo.request.llm.thread;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 更新工作区线程请求VO
 */
@Data
public class UpdateWorkspaceThreadRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作区线程编码(系统业务编码)
     * @mock "WT123456789"
     */
    @NotBlank(message = "工作区线程编码不能为空")
    private String workspaceThreadCode;

    /**
     * 工作区线程名称
     * @mock "更新后的线程名称"
     */
    @NotBlank(message = "工作区线程名称不能为空")
    private String workspaceThreadName;

    /**
     * 工作区线程slug
     * @mock "updated-thread-slug"
     */
    private String workspaceThreadSlug;

    /**
     * 绑定的用户名
     * @mock "admin"
     */
    private String userName;

    /**
     * 工作区slug
     * @mock "workspace-slug-456"
     */
    private String workspaceSlug;
}
