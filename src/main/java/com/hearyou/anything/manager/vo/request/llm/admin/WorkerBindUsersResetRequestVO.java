package com.hearyou.anything.manager.vo.request.llm.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 工作区绑定用户列表 请求VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/12 19:36
 */
@Data
public class WorkerBindUsersResetRequestVO {

    /**
     * 用户 ID 列表，用于指定需要操作的用户
     */
    @JsonProperty("userIds")
    private List<Integer> userIds;

    /**
     * 是否执行重置操作的标志，true 表示重置，false 表示不重置
     */
    @JsonProperty("reset")
    private boolean reset;
}