package com.hearyou.anything.manager.vo.request.business;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.List;

/**
 * 用户信息 请求VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/11 15:38
 */
@Data
public class UserInfoRequestVO{
    /**
     * 用户名
     * @mock test_manager
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     * @mock 123456
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 角色名编码
     * @mock default
     */
    @NotBlank(message = "角色编码不能为空")
    private String roleCode;

    /**
     * 工作区slug列表
     * @mock ["1","2","3"]
     */
    @NotEmpty(message = "工作区slug列表不能为空")
    @Size(min = 1,message = "工作区slug列表不能为空")
    private List<String> workSlugs;
}
