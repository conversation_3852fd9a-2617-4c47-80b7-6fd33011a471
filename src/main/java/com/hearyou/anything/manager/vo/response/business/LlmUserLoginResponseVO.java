package com.hearyou.anything.manager.vo.response.business;

import lombok.Data;
import java.util.List;

/**
 * LLM用户登录 响应VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/16 16:08
 */
@Data
public class LlmUserLoginResponseVO {

    /**
     * 用户真实姓名
     * @mock 邢道荣
     */
    private String name;

    /**
     * 用户名
     * @mock admin
     */
    private String userName;

    /**
     * 用户昵称
     * @mock 上将军丨邢道荣
     */
    private String nickName;

    /**
     * 用户头像url
     * @mock /avatar/2021/07/16/1626461142000.jpg
     */
    private String headImg;

    /**
     * 用户token
     * @mock eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
     */
    private String token;

    /**
     * token过期时间
     * @mock 1626461142000
     */
    private Long expireTime;

    /**
     * 用户角色编码
     * @mock admin
     */
    private String roleCode;

    /**
     * 用户权限编码列表
     * @mock ["user:add","user:edit","user:delete"]
     */
    private List<String> permissionCodeList;

    /**
     * 用户工作区slug编码列表
     * @mock ["group1","group2"]
     */
    private List<String> workSlugList;
}
