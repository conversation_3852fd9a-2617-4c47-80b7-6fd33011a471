package com.hearyou.anything.manager.vo.request.llm.thread;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


import java.io.Serializable;

/**
 * 添加工作区线程请求VO
 */
@Data
public class AddWorkspaceThreadRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作区线程名称
     * @mock "新建线程"
     */
    @NotBlank(message = "工作区线程名称不能为空")
    private String workspaceThreadName;

    /**
     * 工作区线程slug
     * @mock "thread-slug-123"
     */
    private String workspaceThreadSlug;

    /**
     * 绑定的用户名
     * @mock "admin"
     */
    private String userName;

    /**
     * 工作区slug
     * @mock "workspace-slug-456"
     */
    @NotBlank(message = "工作区slug不能为空")
    private String workspaceSlug;
}
