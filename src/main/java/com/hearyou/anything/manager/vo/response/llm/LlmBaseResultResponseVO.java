package com.hearyou.anything.manager.vo.response.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmWorkBindUserResponseVO;
import lombok.Data;

import java.util.List;

/**
 * llm接口基础响应VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/12 19:44
 */
@Data
public class LlmBaseResultResponseVO {
    
    /**
     * 请求处理结果状态
     * true表示成功，false表示失败
     */
    @JsonProperty("success")
    private Boolean success;

    /**
     * 错误信息描述
     * 当success为false时包含错误详情
     */
    @JsonProperty("error")
    private String error;

}