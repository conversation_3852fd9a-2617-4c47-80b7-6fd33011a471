package com.hearyou.anything.manager.vo.response.document;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hearyou.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * <p>
 * Llm工作区文件表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-07-17
 */
@TableName("llm_work_file")
@Data
@ToString(callSuper = true)
public class LlmWorkFileResponseVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 文件编码
	**/
	private String fileCode;
	
	/**
	 * 业务类型
	**/
	private String businessType;
	
	/**
	 * 文件/文件夹名称;如果是文件，则为xxx.jpg，如果是文件夹则是xxx
	**/
	private String fileName;
	
	/**
	 * LLM系统文件名;如果是文件类型，记录llm的文件名称（唯一标识）
	**/
	private String llmFileName;
	
	/**
	 * 上级编码
	**/
	private String parentCode = "0";
	
	/**
	 * 文件大小(byte)
	**/
	private Integer fileSize;
	
	/**
	 * 是否为文件夹;0-否 1-是
	**/
	private Boolean whetherFolder;
	
	/**
	 * 文件md5签名
	**/
	private String fileMd5Sign;
	
	/**
	 * 是否启用;0-false 1-true
	**/
	private Integer whetherEnable;
	
}
