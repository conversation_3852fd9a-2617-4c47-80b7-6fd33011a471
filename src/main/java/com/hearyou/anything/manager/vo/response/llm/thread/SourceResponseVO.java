package com.hearyou.anything.manager.vo.response.llm.thread;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class SourceResponseVO  implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 来源的唯一ID
     * @mock "d673ded0-953e-4d16-98a5-d9eaa6714e62"
     */
    private String id;

    /**
     * 来源的URL或路径
     * @mock
     */
    private String url;

    /**
     * 来源的标题
     * @mock
     */
    private String title;

    /**
     * 文档作者
     * @mock "WPS 文字"
     */
    private String docAuthor;

    /**
     * 描述信息
     * @mock
     */
    private String description;

    /**
     * 文档来源类型
     * @mock "pdf file uploaded by the user."
     */
    private String docSource;

    /**
     * 数据块的来源路径
     * @mock
     */
    private String chunkSource;

    /**
     * 发布日期或时间
     * @mock
     */
    private String published;

    /**
     * 词数
     * @mock 1
     */
    private Integer wordCount;

    /**
     * 预估的Token数量。
     * @mock 3933
     */
    @JsonProperty("token_count_estimate")
    private Integer tokenCountEstimate;

    /**
     * 来源的原始文本内容
     * @mock "<document_metadata>..."
     */
    private String text;

    /**
     * 相关性距离分数。
     * JSON字段名以"_"开头
     * @mock 0.6238386631011963
     */
    @JsonProperty("_distance")
    private Double distance;

    /**
     * 相关性得分
     * @mock 0.3761613368988037
     */
    private Double score;
}
