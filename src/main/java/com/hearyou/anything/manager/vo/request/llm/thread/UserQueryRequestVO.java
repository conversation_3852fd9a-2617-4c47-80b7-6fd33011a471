package com.hearyou.anything.manager.vo.request.llm.thread;

import com.hearyou.anything.manager.vo.response.llm.thread.AttachmentResponseVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class UserQueryRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户发送的文本消息
     * @mock "What is AnythingLLM?"
     */
    private String message;

    /**
     * 请求的模式，例如 'query' 或 'chat'
     * @mock "query | chat"
     */
    private String mode = "chat";

    /**
     * 用户的唯一ID
     * @mock 1
     */
    private Integer userId = 1;

    /**
     * 附件列表，可以包含一个或多个附件对象
     */
    private List<AttachmentResponseVO> attachments = new ArrayList<>();

    /**
     * 是否重置对话状态的标志
     * @mock false
     */
    private boolean reset = false;
}
