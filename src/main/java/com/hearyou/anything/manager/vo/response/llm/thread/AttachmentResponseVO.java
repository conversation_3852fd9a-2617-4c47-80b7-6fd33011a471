package com.hearyou.anything.manager.vo.response.llm.thread;
import lombok.Data;

import java.io.Serializable;

@Data
public class AttachmentResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件名
     * @mock "image.png"
     */
    private String name;

    /**
     * 文件的MIME类型
     * @mock "image/png"
     */
    private String mime;

    /**
     * 文件内容的Base64编码字符串
     * @mock "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
     */
    private String contentString;
}
