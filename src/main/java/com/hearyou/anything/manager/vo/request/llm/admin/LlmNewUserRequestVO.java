package com.hearyou.anything.manager.vo.request.llm.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * llm用户请求VO
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/5 18:12
 */
@Data
public class LlmNewUserRequestVO {

    /**
     * 用户名
     * @mock test_manager
     */
    @JsonProperty("username")
    private String username;

    /**
     * 密码
     * @mock 123456
     */
    @JsonProperty("password")
    private String password;

    /**
     * 角色名组合
     * @mock default
     */
    @JsonProperty("role")
    private String role;
}
