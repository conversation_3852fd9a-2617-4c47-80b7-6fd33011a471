package com.hearyou.anything.manager.vo.response.llm.thread;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class ChatContentResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    // --- 通用字段 ---
    /**
     * 角色，'user' 或 'assistant'
     */
    @JsonProperty("role")
    private String role;

    /**
     * 消息内容
     */
    @JsonProperty("content")
    private String content;

    /**
     * 发送时间的Unix时间戳
     * @mock 1752040807
     */
    @JsonProperty("sentAt")
    private Long sentAt;

    /**
     * 对话ID
     * @mock 58
     */
    @JsonProperty("chatId")
    private Integer chatId;

    // --- 用户消息特有字段 ---
    /**
     * 附件列表，由于示例中为空
     */
    @JsonProperty("attachments")
    private List<AttachmentResponseVO> attachments;

    // --- AI助手消息特有字段 ---
    /**
     * 消息类型，例如 'chat'
     */
    @JsonProperty("type")
    private String type;

    /**
     * 引用来源列表，由于示例中为空
     */
    @JsonProperty("sources")
    private List<SourceResponseVO> sources;

    /**
     * 反馈评分，可为空
     * @mock null
     */
    @JsonProperty("feedbackScore")
    private Integer feedbackScore;

    /**
     * 性能指标对象
     */
    @JsonProperty("metrics")
    private MetricsResponseVO metrics;

}
