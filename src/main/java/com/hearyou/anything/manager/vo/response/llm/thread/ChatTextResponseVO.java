package com.hearyou.anything.manager.vo.response.llm.thread;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ChatTextResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 响应的唯一ID
     * @mock "4779c511-c1fa-4dfb-9ad7-2df0db2cb54f"
     */
    private String id;

    /**
     * 响应类型
     * @mock "textResponse"
     */
    private String type;

    /**
     * 是否关闭连接的标志
     * @mock true
     */
    private boolean close;

    /**
     * 错误信息，可为空。
     * @mock null
     */
    private Object error;

    /**
     * 对话ID
     * @mock 497
     */
    private Integer chatId;

    /**
     * AI返回的文本响应内容
     * @mock "<think>......"
     */
    private String textResponse;

    /**
     * 引用来源列表，由于示例中为空
     */
    private List<SourceResponseVO> sources;

    /**
     * 嵌套的性能指标对象
     */
    private MetricsResponseVO metrics;
}
