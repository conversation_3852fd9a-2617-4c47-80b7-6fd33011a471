package com.hearyou.anything.manager;

import com.hearyou.common.minio.annotation.EnableMinioClient;
import com.hearyou.common.utils.SpringContextUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication(scanBasePackages = "com.hearyou.anything.manager")
@MapperScan("com.hearyou.anything.manager.mapper")
@EnableMinioClient
public class AnythingLlmApiManagerApplication {

	public static void main(String[] args) {
		ConfigurableApplicationContext applicationContext = SpringApplication.run(AnythingLlmApiManagerApplication.class, args);;
		SpringContextUtil.setApplicationContext(applicationContext);
	}

}
