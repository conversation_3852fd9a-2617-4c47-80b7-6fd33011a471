package com.hearyou.anything.manager.utils;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;
import java.io.File;
import java.net.URI;
import java.util.Map;

public class RestClientUtil {
    private static volatile RestClient defaultClient;

    // 初始化默认Client（按需加载）
    private static RestClient getDefaultClient() {
        if (defaultClient == null) {
            synchronized (RestClientUtil.class) {
                if (defaultClient == null) {
                    defaultClient = RestClient.builder()
                        .defaultStatusHandler(HttpStatusCode::isError, (req, res) -> {
                            throw new RestClientException(res.getStatusCode(), "HTTP Error");
                        })
                        .build();
                }
            }
        }
        return defaultClient;
    }

    //=============== 基础操作 ================
    
    // GET无参
    public static <T> T get(String uri, Class<T> responseType) {
        return executeWithClient(null).get()
            .uri(uri)
            .retrieve()
            .body(responseType);
    }

    // GET带查询参数
    public static <T> T get(String uri, Map<String, Object> params, Class<T> responseType) {
        return executeWithClient(null).get()
            .uri(buildUri(uri, params))
            .retrieve()
            .body(responseType);
    }

    // GET带Header和参数
    public static <T> T getWithHeaders(String uri, Map<String, String> headers, 
                                      Map<String, Object> params, Class<T> responseType) {
        return executeWithClient(null).get()
            .uri(buildUri(uri, params))
            .headers(h -> h.setAll(headers))
            .retrieve()
            .body(responseType);
    }

    //=============== 带自定义Client的操作 ================
    
    // 使用自定义Client的GET
    public static <T> T get(String uri, Map<String, Object> params, 
                           Class<T> responseType, RestClient customClient) {
        return executeWithClient(customClient).get()
            .uri(buildUri(uri, params))
            .retrieve()
            .body(responseType);
    }

    //=============== 数据修改操作 ================
    
    // POST基础版
    public static <T> T post(String uri, Object body, Class<T> responseType) {
        return executeWithClient(null).post()
            .uri(uri)
            .contentType(MediaType.APPLICATION_JSON)
            .body(body)
            .retrieve()
            .body(responseType);
    }

    // POST带Header（自定义Client版本）
    public static <T> T post(String uri, Map<String, String> headers, Object body,
                           Class<T> responseType, RestClient customClient) {
        return executeWithClient(customClient).post()
            .uri(uri)
            .headers(h -> h.setAll(headers))
            .contentType(MediaType.APPLICATION_JSON)
            .body(body)
            .retrieve()
            .body(responseType);
    }

    //=============== 文件操作 ================
    
    // 文件上传（通用方法）
    public static Map<String, Object> uploadFile(String uri, File file, String fileName,
                                               Map<String, String> formFields, RestClient client) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new FileSystemResource(file) {
            @Override
            public String getFilename() {
                return fileName;
            }
        });
        formFields.forEach(body::add);

        return executeWithClient(client).post()
            .uri(uri)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(body)
            .retrieve()
            .body(new ParameterizedTypeReference<Map<String, Object>>() {});
    }

    //=============== 工具方法 ================
    
    private static RestClient executeWithClient(RestClient customClient) {
        return customClient != null ? customClient : getDefaultClient();
    }

    private static URI buildUri(String baseUri, Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(baseUri);
        params.forEach((key, value) -> {
            if (value instanceof Iterable) {
                ((Iterable<?>) value).forEach(v -> builder.queryParam(key, v));
            } else {
                builder.queryParam(key, value);
            }
        });
        return builder.build().toUri();
    }

    //=============== 配置方法 ================
    
    public static void configDefaultClient(RestClient.Builder builder) {
        synchronized (RestClientUtil.class) {
            defaultClient = builder.build();
        }
    }
}

// 自定义异常类
class RestClientException extends RuntimeException {
    private final HttpStatusCode statusCode;
    
    public RestClientException(HttpStatusCode statusCode, String message) {
        super(message);
        this.statusCode = statusCode;
    }
}
