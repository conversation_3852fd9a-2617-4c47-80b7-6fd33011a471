package com.hearyou.anything.manager.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.function.Consumer;

/**
 * SSE流式请求工具类
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/9 17:00
 */
@Slf4j
public class SseStreamUtil {

    /**
     * 发送流式POST请求
     * @param webClient WebClient实例
     * @param url 完整的请求URL
     * @param token 认证token
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @param chunkConsumer 处理每个数据块的回调函数
     * @param <T> 响应类型
     */
    public static <T> void postStreamResult(WebClient webClient, String url, String token,
                                          Object requestBody, Class<T> responseType,
                                          Consumer<T> chunkConsumer) {
        postStreamResult(webClient, url, token, requestBody, responseType, chunkConsumer, new ObjectMapper());
    }

    /**
     * 发送流式POST请求（带自定义ObjectMapper）
     * @param webClient WebClient实例
     * @param url 完整的请求URL
     * @param token 认证token
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @param chunkConsumer 处理每个数据块的回调函数
     * @param objectMapper JSON序列化器
     * @param <T> 响应类型
     */
    public static <T> void postStreamResult(WebClient webClient, String url, String token,
                                          Object requestBody, Class<T> responseType,
                                          Consumer<T> chunkConsumer, ObjectMapper objectMapper) {
        log.info("开始流式POST请求: {}", url);
        log.debug("请求体: {}", requestBody);

        try {
            Flux<String> sseFlux = webClient.post()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .header(HttpHeaders.ACCEPT, "text/event-stream")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header(HttpHeaders.CACHE_CONTROL, "no-cache")
                .header("Connection", "keep-alive")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToFlux(String.class)
                .timeout(Duration.ofMinutes(5))
                .doOnSubscribe(subscription -> log.info("SSE流订阅开始"))
                .doOnNext(chunk -> {
                    log.debug("收到原始SSE数据块 [长度={}]: {}", chunk.length(), chunk);
                    try {
                        processSseChunk(chunk, responseType, chunkConsumer, objectMapper);
                    } catch (Exception e) {
                        log.error("处理SSE数据块失败: {}", e.getMessage(), e);
                    }
                })
                .doOnError(error -> log.error("SSE流请求出错: {}", error.getMessage(), error))
                .doOnComplete(() -> log.info("SSE流请求完成"))
                .doFinally(signalType -> log.info("SSE流结束，信号类型: {}", signalType));

            // 使用blockLast()来阻塞等待流完成，确保所有数据都被处理
            sseFlux.blockLast(Duration.ofMinutes(5));
            log.info("SSE流处理完成");

        } catch (Exception e) {
            log.error("流式请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("流式请求失败", e);
        }
    }

    /**
     * 处理SSE数据块
     * @param chunk 原始数据块
     * @param responseType 响应类型
     * @param chunkConsumer 数据块消费者
     * @param objectMapper JSON序列化器
     * @param <T> 响应类型
     */
    private static <T> void processSseChunk(String chunk, Class<T> responseType, 
                                          Consumer<T> chunkConsumer, ObjectMapper objectMapper) {
        if (chunk == null || chunk.trim().isEmpty()) {
            log.debug("收到空的数据块，跳过处理");
            return;
        }

        log.debug("开始处理数据块，原始内容: [{}]", chunk);

        // 首先检查是否是直接的JSON格式（不是标准SSE格式）
        String trimmedChunk = chunk.trim();
        if (trimmedChunk.startsWith("{") && trimmedChunk.endsWith("}")) {
            log.debug("检测到直接JSON格式，尝试解析");
            try {
                // 直接解析JSON
                T dataObject = objectMapper.readValue(trimmedChunk, responseType);
                log.debug("直接JSON反序列化成功，对象类型: {}", dataObject.getClass().getSimpleName());

                // 通过回调函数传递数据
                chunkConsumer.accept(dataObject);
                log.debug("直接JSON数据块处理完成并传递给消费者");
                return;

            } catch (Exception e) {
                log.warn("直接JSON解析失败，尝试SSE格式解析: {}", e.getMessage());
            }
        }

        // 处理标准SSE格式的数据
        processStandardSseFormat(chunk, responseType, chunkConsumer, objectMapper);
    }

    /**
     * 处理标准SSE格式的数据
     */
    private static <T> void processStandardSseFormat(String chunk, Class<T> responseType,
                                                   Consumer<T> chunkConsumer, ObjectMapper objectMapper) {
        String[] lines = chunk.split("\n");
        log.debug("数据块分割为 {} 行", lines.length);

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            log.debug("处理第 {} 行: [{}]", i + 1, line);

            // 跳过空行和注释行
            if (line.isEmpty()) {
                log.debug("跳过空行");
                continue;
            }

            if (line.startsWith(":")) {
                log.debug("跳过注释行: {}", line);
                continue;
            }

            // 处理 "data: " 开头的数据行
            if (line.startsWith("data: ")) {
                String jsonData = line.substring(6); // 移除 "data: " 前缀
                log.debug("提取JSON数据: [{}]", jsonData);

                // 跳过 "[DONE]" 标记
                if ("[DONE]".equals(jsonData.trim())) {
                    log.info("收到SSE结束标记 [DONE]");
                    continue;
                }

                try {
                    // 将JSON字符串反序列化为指定类型
                    T dataObject = objectMapper.readValue(jsonData, responseType);
                    log.debug("SSE JSON反序列化成功，对象类型: {}", dataObject.getClass().getSimpleName());

                    // 通过回调函数传递数据
                    chunkConsumer.accept(dataObject);
                    log.debug("SSE数据块处理完成并传递给消费者");

                } catch (Exception e) {
                    log.error("解析SSE数据块失败: {}, 原始JSON: [{}]", e.getMessage(), jsonData, e);
                }
            } else {
                log.debug("非data行，内容: [{}]", line);
            }
        }
    }
}
