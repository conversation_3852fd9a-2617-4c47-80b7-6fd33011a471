package com.hearyou.anything.manager.uaa;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.google.common.collect.Lists;
import com.hearyou.anything.manager.entity.SysRolePermissionEntity;
import com.hearyou.anything.manager.llm.LlmWorkspaceApi;
import com.hearyou.anything.manager.vo.response.business.LlmUserInfoResponseVO;
import com.hearyou.anything.manager.vo.response.business.LlmUserLoginResponseVO;
import com.hearyou.common.base.PageResult;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.convert.LlmUserConvert;
import com.hearyou.anything.manager.entity.LlmUserWorkEntity;
import com.hearyou.anything.manager.entity.SysUserRoleEntity;
import com.hearyou.anything.manager.entity.UserInfoEntity;
import com.hearyou.anything.manager.enums.LlmRoleEnum;
import com.hearyou.anything.manager.llm.LlmAdminApi;
import com.hearyou.anything.manager.service.*;
import com.hearyou.anything.manager.vo.request.business.LoginRequestVO;
import com.hearyou.anything.manager.vo.request.business.UserInfoRequestVO;
import com.hearyou.anything.manager.vo.request.llm.admin.LlmNewUserRequestVO;
import com.hearyou.anything.manager.vo.request.llm.admin.WorkerBindUsersResetRequestVO;
import com.hearyou.anything.manager.vo.response.llm.LlmBaseResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmNewUserResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmNewUserResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmUsersResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmWorkBindUsersResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResponseVO;
import com.hearyou.common.utils.validate.ValidateUtil;
import com.hearyou.common.utils.validate.ValidationResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * llm用户uaa业务实现类
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/9 18:57
 * @apiNote llm的角色是固定的三个角色，这里不需要角色服务
 */
@Component
public class LlmUseruaaBusiness implements LlmUseruaaFacade{

    private LlmUserConvert llmUserConvert;

    private UserInfoService userInfoService;

    private SysUserRoleService sysUserRoleService;

    private LlmUserWorkService llmUserWorkService;

    private SysPermissionService sysPermissionService;

    private SysRolePermissionService sysRolePermissionService;

    private LlmAdminApi llmAdminApi;

    private LlmWorkspaceApi llmWorkspaceApi;

    @Autowired
    public LlmUseruaaBusiness(LlmUserConvert llmUserConvert,UserInfoService userInfoService, SysUserRoleService sysUserRoleService,
                              LlmUserWorkService llmUserWorkService,SysPermissionService sysPermissionService,
                              SysRolePermissionService sysRolePermissionService, LlmAdminApi llmAdminApi,
                              LlmWorkspaceApi llmWorkspaceApi) {
        this.llmUserConvert = llmUserConvert;
        this.userInfoService = userInfoService;
        this.sysUserRoleService = sysUserRoleService;
        this.llmUserWorkService = llmUserWorkService;
        this.sysPermissionService = sysPermissionService;
        this.sysRolePermissionService = sysRolePermissionService;
        this.llmAdminApi = llmAdminApi;
        this.llmWorkspaceApi = llmWorkspaceApi;
    }

    /**
     * 创建llm用户 并 绑定角色和工作区权限
     * @param requestVO
     * @return
     */
    @Override
    public Result<Boolean> saveUserBind(UserInfoRequestVO requestVO) {
        if(null == requestVO){
            return Result.failure("请求参数不能为空!");
        }
        final String username = requestVO.getUsername();
        //校验当前用户是否存在
        LambdaQueryWrapper<UserInfoEntity> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(UserInfoEntity::getUserName,username);
        long count = userInfoService.count(userQueryWrapper);
        if(count > 0){
            return Result.failure("创建用户失败，当前用户名已存在!");
        }

        //这里不去查询llm接口获取用户列表再去二次过滤了，直接认为用户不存在
        //2.获取 llm工作区列表，校验需要绑定的工作区是否存在
        Result<WorkspacesResponseVO> workspacesResult = llmWorkspaceApi.getWorkspaces();
        if(workspacesResult.isFailure()){
            return Result.failure("创建用户失败, 获取工作区列表失败!");
        }
        WorkspacesResponseVO workspaces = workspacesResult.getData();
        //提取所有的slug
        final Set<String> slugSet = workspaces.getWorkspaces()
                .stream()
                .map(workspace -> workspace.getSlug())
                .collect(Collectors.toSet());
        List<String> notExistSlugs = requestVO.getWorkSlugs().stream().filter(slug -> !slugSet.contains(slug)).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(notExistSlugs)){
            return Result.failure("创建用户失败,以下工作区:" + notExistSlugs + "不存在!");
        }

        //校验角色编码
        LlmRoleEnum roleEnum = LlmRoleEnum.getEnumByRoleCode(requestVO.getRoleCode());
        if(null == roleEnum){
            return Result.failure("创建用户失败,角色编码不存在!");
        }

        //3.调用llm的创建用户接口
        LlmNewUserRequestVO newUserRequestVO = llmUserConvert.saveUserRequestVO2LlmNewUserRequestVO(requestVO);
        Result<LlmNewUserResultResponseVO> newLlmUserResult = llmAdminApi.addUser(newUserRequestVO);
        if(newLlmUserResult.isFailure()){
            return Result.failure("创建用户失败:" + newLlmUserResult.getMessage());
        }
        LlmNewUserResultResponseVO llmUserResult = newLlmUserResult.getData();
        LlmNewUserResponseVO llmUser = llmUserResult.getUser();

        //4.循环调用llm的 工作区绑定用户列表的接口，完成工作区权限的绑定 == 这里的userId是创建的llm用户id，slug是工作区的slug
        final Integer llmUserId = llmUser.getId();
        for (String workSlug : requestVO.getWorkSlugs()) {
            WorkerBindUsersResetRequestVO workerBindUsers = new WorkerBindUsersResetRequestVO();
            workerBindUsers.setUserIds(Lists.newArrayList(llmUserId));
            workerBindUsers.setReset(false);//不去重置已经绑定的用户
            Result<LlmWorkBindUsersResultResponseVO> bindUsersResultResponseVOResult = llmAdminApi.bindUserToWorkspace(workSlug, workerBindUsers);
            if(bindUsersResultResponseVOResult.isFailure()){
                return Result.failure("创建Llm用户失败，绑定工作区权限失败!");
            }
        }

        //5.将用户信息保存到数据库中，包含用户名、密码等，并保存llm的userId
        UserInfoEntity userInfoEntity = llmUserConvert.llmNewUserResponseVO2UserInfoEntity(requestVO,llmUserId);
        userInfoService.save(userInfoEntity);

        //6.批量保存用户角色 绑定关系
        SysUserRoleEntity sysUserRoleEntity = llmUserConvert.userAndRole2SysUserRole(userInfoEntity.getUserName(), roleEnum);
        sysUserRoleService.save(sysUserRoleEntity);

        //7.批量保存用户 工作区绑定关系
        List<LlmUserWorkEntity> llmUserWorkList = llmUserConvert.userAndWorkslugList2LlmUserWorkList(userInfoEntity.getUserName(), requestVO.getWorkSlugs());
        llmUserWorkService.saveBatch(llmUserWorkList);
        return Result.success(Boolean.TRUE);
    }

    /**
     * 更新用户状态
     * @param username       用户名|admin
     * @param disabledStatus 禁用状态|true
     * @return
     */
    @Override
    public Result<Boolean> updateUserStatus(String username, Boolean disabledStatus) {
        if(StrUtil.isBlank(username)){
            return Result.failure("修改用户状态失败!用户名不能为空!");
        }
        LambdaUpdateWrapper<UserInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserInfoEntity::getUserName,username);
        updateWrapper.set(UserInfoEntity::getStatus,disabledStatus ? 3 : 0);//0-启用 3-禁用
        boolean updateResult = userInfoService.update(updateWrapper);
        return Result.success(updateResult);
    }

    /**
     * 删除用户
     * @param username 用户名|admin
     * @return
     */
    @Override
    public Result<Boolean> deleteUser(String username) {
        if(StrUtil.isBlank(username)){
            return Result.failure("删除用户失败!用户名不能为空!");
        }
        //1.查询用户是否存在，存在的话，调用llm删除用户接口，再去删除数据库的数据
        Result<LlmUsersResponseVO> usersResult = llmAdminApi.getUsers();
        if(usersResult.isFailure()){
            return Result.failure("删除用户失败!获取用户列表失败!");
        }
        //1.1提取用户名
        Set<String> usernameSet = usersResult.getData().getUsers().stream().map(user -> user.getUserName()).collect(Collectors.toSet());
        if(!usernameSet.contains(username)){
            return Result.failure("删除用户失败!用户不存在!");
        }

        //1.2查询当前数据库是否存在，如果都不存在，则直接返回成功
        LambdaQueryWrapper<UserInfoEntity> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(UserInfoEntity::getUserName,username);
        UserInfoEntity userInfoEntity = userInfoService.getOne(userQueryWrapper);
        //我们数据库中不存在这个用户的话，直接返回成功，说明这个用户可能是直接在llm系统创建的，不是走的我们接口
        if (null == userInfoEntity) {
            return Result.success(Boolean.TRUE);
        }
        //两个系统都没有这个用户的话，直接返回
        if(!usernameSet.contains(username)){
            return Result.success(Boolean.TRUE);
        }

        //我们系统有这个用户，且llm系统也有这个用户的话，先调用llm的接口处理用户和绑定，再去删除llm用户，最后再去删除我们数据库中的用户绑定和用户信息
        //查询用户之前绑定的工作区
        LambdaQueryWrapper<LlmUserWorkEntity> llmUserWorkQueryWrapper = new LambdaQueryWrapper<>();
        llmUserWorkQueryWrapper.eq(LlmUserWorkEntity::getUserName,userInfoEntity.getUserName());
        List<LlmUserWorkEntity> userWorkList = llmUserWorkService.list(llmUserWorkQueryWrapper);
        //提取绑定的slug列表
        final List<String> bindWorkSlugList = Optional.ofNullable(userWorkList).orElse(new ArrayList<>()).stream()
                .map(entity -> entity.getWorkSlug()).collect(Collectors.toList());
        if(usernameSet.contains(username)){
            //调用llm接口，循环工作区绑定用户列表的接口，由于不能直接删除当前工作区下某个绑定的用户，只能是选择是否重置所有的绑定关系，因此需要查询当前工作区的所有除自己之外的用户，然后重新进行绑定
            for (String workSlug : bindWorkSlugList) {
                LambdaQueryWrapper<LlmUserWorkEntity> otherUserWorkQueryWrapper = new LambdaQueryWrapper<>();
                otherUserWorkQueryWrapper.eq(LlmUserWorkEntity::getWorkSlug,workSlug);
                otherUserWorkQueryWrapper.notIn(LlmUserWorkEntity::getUserName,userInfoEntity.getUserName());
                List<LlmUserWorkEntity> otherUserWorkList = llmUserWorkService.list(otherUserWorkQueryWrapper);
                List<String> userNameList = otherUserWorkList.stream().map(entity -> entity.getUserName()).collect(Collectors.toList());

                //再去查询用户列表，获取这些用户对应的llm_userid
                LambdaQueryWrapper<UserInfoEntity> userQueryWrapper2 = new LambdaQueryWrapper<>();
                userQueryWrapper2.in(UserInfoEntity::getUserName,userNameList);
                List<UserInfoEntity> userInfoEntityList = userInfoService.list(userQueryWrapper2);

                //提取所有的llm_userid
                List<Integer> llmUserIds = userInfoEntityList.stream().map(entity -> Integer.parseInt(entity.getLlmUserId())).collect(Collectors.toList());

                //提取所有绑定当前工作区的其他用户列表，重新进行绑定
                WorkerBindUsersResetRequestVO workerBindUsersResetRequestVO = new WorkerBindUsersResetRequestVO();
                workerBindUsersResetRequestVO.setReset(true);
                workerBindUsersResetRequestVO.setUserIds(llmUserIds);
                llmAdminApi.bindUserToWorkspace(workSlug, workerBindUsersResetRequestVO);
            }

            //如果llm系统存在这个用户，就调用llm的删除用户接口
            final String llmUserId = userInfoEntity.getLlmUserId();
            Result<LlmBaseResultResponseVO> deleteUserResult = llmAdminApi.deleteUserByUserId(llmUserId);
            if(deleteUserResult.isFailure() || !deleteUserResult.getData().getSuccess()){
                return Result.failure("LLM删除用户失败!");
            }
        }

        //删除用户 角色表信息
        LambdaQueryWrapper<SysUserRoleEntity> sysUserRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserRoleLambdaQueryWrapper.eq(SysUserRoleEntity::getUserName,userInfoEntity.getUserName());
        sysUserRoleService.remove(sysUserRoleLambdaQueryWrapper);
        //删除当前用户-工作区绑定信息
        llmUserWorkService.remove(llmUserWorkQueryWrapper);
        //删除用户
        userInfoService.remove(userQueryWrapper);

        return Result.success(Boolean.TRUE);
    }

    /**
     * 更新用户信息
     * @param requestVO
     * @return
     */
    @Override
    public Result<Boolean> updateUserBind(UserInfoRequestVO requestVO) {
        final String username = requestVO.getUsername();
        //1.1 校验角色编码
        LlmRoleEnum roleEnum = LlmRoleEnum.getEnumByRoleCode(requestVO.getRoleCode());
        if(null == roleEnum){
            return Result.failure("修改用户信息失败,角色编码不存在!");
        }

        //1.2 查询当前数据库是否存在，如果不存在，则直接返回成功
        LambdaQueryWrapper<UserInfoEntity> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(UserInfoEntity::getUserName,username);
        UserInfoEntity userInfoEntity = userInfoService.getOne(userQueryWrapper);
        //我们数据库中不存在这个用户的话，直接返回成功，说明这个用户可能是直接在llm系统创建的，不是走的我们接口
        if (null == userInfoEntity) {
            return Result.success(Boolean.TRUE);
        }

        //1.3 查询用户之前绑定的工作区
        LambdaQueryWrapper<LlmUserWorkEntity> llmUserWorkQueryWrapper = new LambdaQueryWrapper<>();
        llmUserWorkQueryWrapper.eq(LlmUserWorkEntity::getUserName,userInfoEntity.getUserName());
        List<LlmUserWorkEntity> oldUserWorkList = llmUserWorkService.list(llmUserWorkQueryWrapper);

        //1.4 提取绑定的slug列表
        final List<String> bindWorkSlugList = Optional.ofNullable(oldUserWorkList).orElse(new ArrayList<>()).stream()
                .map(entity -> entity.getWorkSlug()).collect(Collectors.toList());
        //如果要修改的话，需要找到变更的工作区有哪些，llm系统那边没办法直接删除某个工作区下某个用户，因此原来绑定后，且修改以后还绑定的工作区不需要进行变更操作
        List<String> workSlugs = requestVO.getWorkSlugs();
        //找到要删除的工作区
        List<String> delWorkSlugList = bindWorkSlugList.stream().filter(slug -> !workSlugs.contains(slug)).collect(Collectors.toList());
        //找到要新增绑定的工作区
        List<String> addWorkSlugList = workSlugs.stream().filter(slug -> !bindWorkSlugList.contains(slug)).collect(Collectors.toList());

        //1.5 循环遍历以前绑定的所有需要变动的工作区slug，通过llm接口进行变动，重置以前的所有用户绑定关系(重置所有绑定关系，而不是删除某个工作区下某个用户，没有这个功能)
        for (String delWorkSlug : delWorkSlugList) {
            //需要查询除了当前用户外所有的绑定用户，构建新的重置 绑定信息实体
            LambdaQueryWrapper<LlmUserWorkEntity> notUserWorkQueryWrapper = new LambdaQueryWrapper<>();
            notUserWorkQueryWrapper.notIn(LlmUserWorkEntity::getUserName,userInfoEntity.getUserName());
            notUserWorkQueryWrapper.eq(LlmUserWorkEntity::getWorkSlug,delWorkSlug);
            List<LlmUserWorkEntity> notUserWorkList = llmUserWorkService.list(notUserWorkQueryWrapper);
            //提取所有用户名
            List<String> userNameList = Optional.ofNullable(notUserWorkList).orElse(new ArrayList<>()).stream().map(entity -> entity.getUserName()).collect(Collectors.toList());

            //再去查询用户列表，获取这些用户对应的llm_userid
            LambdaQueryWrapper<UserInfoEntity> userQueryWrapper2 = new LambdaQueryWrapper<>();
            userQueryWrapper2.in(UserInfoEntity::getUserName,userNameList);
            List<UserInfoEntity> userInfoEntityList = userInfoService.list(userQueryWrapper2);
            //提取所有的llm_userid
            List<Integer> llmUserIds = userInfoEntityList.stream().map(entity -> Integer.parseInt(entity.getLlmUserId())).collect(Collectors.toList());

            //进行构建绑定
            WorkerBindUsersResetRequestVO workerBindUsersResetRequestVO = new WorkerBindUsersResetRequestVO();
            workerBindUsersResetRequestVO.setReset(true);
            workerBindUsersResetRequestVO.setUserIds(llmUserIds);
            llmAdminApi.bindUserToWorkspace(delWorkSlug, workerBindUsersResetRequestVO);
        }

        for (String addWorkSlug : addWorkSlugList) {
            //需要查询除了当前用户外所有的绑定用户，构建新的重置 绑定信息实体
            LambdaQueryWrapper<LlmUserWorkEntity> addUserWorkQueryWrapper = new LambdaQueryWrapper<>();
            addUserWorkQueryWrapper.eq(LlmUserWorkEntity::getWorkSlug,addWorkSlug);
            List<LlmUserWorkEntity> addUserWorkList = llmUserWorkService.list(addUserWorkQueryWrapper);
            //提取所有用户名
            List<String> userNameList = Optional.ofNullable(addUserWorkList).orElse(new ArrayList<>()).stream().map(entity -> entity.getUserName()).collect(Collectors.toList());

            //再去查询用户列表，获取这些用户对应的llm_userid
            LambdaQueryWrapper<UserInfoEntity> userQueryWrapper2 = new LambdaQueryWrapper<>();
            userQueryWrapper2.in(UserInfoEntity::getUserName,userNameList);
            List<UserInfoEntity> userInfoEntityList = userInfoService.list(userQueryWrapper2);
            //提取所有的llm_userid
            List<Integer> llmUserIds = userInfoEntityList.stream().map(entity -> Integer.parseInt(entity.getLlmUserId())).collect(Collectors.toList());
            //添加当前用户
            llmUserIds.add(Integer.parseInt(userInfoEntity.getLlmUserId()));

            //进行构建绑定
            WorkerBindUsersResetRequestVO workerBindUsersResetRequestVO = new WorkerBindUsersResetRequestVO();
            workerBindUsersResetRequestVO.setReset(true);
            workerBindUsersResetRequestVO.setUserIds(llmUserIds);
            llmAdminApi.bindUserToWorkspace(addWorkSlug, workerBindUsersResetRequestVO);
        }

        //删除用户 角色表信息
        LambdaQueryWrapper<SysUserRoleEntity> sysUserRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserRoleLambdaQueryWrapper.eq(SysUserRoleEntity::getUserName,userInfoEntity.getUserName());
        sysUserRoleService.remove(sysUserRoleLambdaQueryWrapper);
        //删除当前用户-工作区绑定信息
        llmUserWorkService.remove(llmUserWorkQueryWrapper);

        //6.批量保存用户角色 绑定关系
        SysUserRoleEntity sysUserRoleEntity = llmUserConvert.userAndRole2SysUserRole(userInfoEntity.getUserName(), roleEnum);
        sysUserRoleService.save(sysUserRoleEntity);

        //7.批量保存用户 工作区绑定关系
        List<LlmUserWorkEntity> llmUserWorkList = llmUserConvert.userAndWorkslugList2LlmUserWorkList(userInfoEntity.getUserName(), requestVO.getWorkSlugs());
        llmUserWorkService.saveBatch(llmUserWorkList);

        //8. 更新用户信息
        LambdaUpdateWrapper<UserInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserInfoEntity::getUserName,username);
        updateWrapper.set(UserInfoEntity::getPassword,requestVO.getPassword());
        userInfoService.update(updateWrapper);
        return Result.success(Boolean.TRUE);
    }

    /**
     * 登录
     * @param requestVO
     * @return
     */
    @Override
    public Result<LlmUserLoginResponseVO> login(LoginRequestVO requestVO) {
        if(null == requestVO){
            return Result.failure("登录失败!登录信息不能为空!");
        }
        ValidationResult validationResult = ValidateUtil.validateEntity(requestVO);
        if (validationResult.isHasErrors()){
            return Result.failure("登录失败!" + validationResult.getMessage());
        }

        //查询当前用户信息
        LambdaQueryWrapper<UserInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfoEntity::getUserName,requestVO.getUsername());
        UserInfoEntity userInfoEntity = userInfoService.getOne(queryWrapper);
        if(null == userInfoEntity){
            return Result.failure("登录失败!用户不存在!");
        }
        //判断密码是否一致
        if(!requestVO.getPassword().equals(userInfoEntity.getPassword())){
            return Result.failure("登录失败!密码错误!");
        }

        //登录成功
        StpUtil.login(requestVO.getUsername());
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();

        //查询用户的角色信息 和 权限列表 以及工作区slug列表
        LambdaQueryWrapper<SysUserRoleEntity> userRoleQueryWrapper = new LambdaQueryWrapper<>();
        userRoleQueryWrapper.eq(SysUserRoleEntity::getUserName,requestVO.getUsername());
        SysUserRoleEntity userRoleEntity = sysUserRoleService.getOne(userRoleQueryWrapper);
        if(null == userRoleEntity){
            return Result.failure("登录失败!当前用户的角色信息不存在!");
        }

        LambdaQueryWrapper<SysRolePermissionEntity> rolePermissionEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        rolePermissionEntityLambdaQueryWrapper.eq(SysRolePermissionEntity::getRoleCode,userRoleEntity.getRoleCode());
        List<SysRolePermissionEntity> rolePermissionEntityList = sysRolePermissionService.list(rolePermissionEntityLambdaQueryWrapper);
        //提取权限列表
        final List<String> permissionCodeList = Optional.ofNullable(rolePermissionEntityList).orElse(new ArrayList<>()).stream().map(entity -> entity.getPermissionCode()).collect(Collectors.toList());

        LambdaQueryWrapper<LlmUserWorkEntity> userWorkQueryWrapper = new LambdaQueryWrapper<>();
        userWorkQueryWrapper.eq(LlmUserWorkEntity::getUserName,requestVO.getUsername());
        List<LlmUserWorkEntity> userWorkList = llmUserWorkService.list(userWorkQueryWrapper);

        //提取工作区slug列表
        final List<String> workSlugList = Optional.ofNullable(userWorkList).orElse(new ArrayList<>()).stream().map(entity -> entity.getWorkSlug()).collect(Collectors.toList());
        LlmUserLoginResponseVO userLoginResponseVO = llmUserConvert.userinfoAndRolePermissionAndWorkSlugList2TokenInfo(userInfoEntity, userRoleEntity.getRoleCode(), permissionCodeList, workSlugList, tokenInfo);
        return Result.success(userLoginResponseVO);
    }


    /**
     * 根据用户名查询所属的工作区列表
     * @param username 用户名
     * @return
     */
    @Override
    public Result<List<String>> findWorkSlugs(String username) {
        if (StrUtil.isBlank(username)) {
            return Result.failure("用户名不能为空");
        }

        QueryWrapper<LlmUserWorkEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_name", username);

        List<LlmUserWorkEntity> list = llmUserWorkService.list(wrapper);

        List<String> workSlugList = new ArrayList<>();
        for (LlmUserWorkEntity llmUserWorkEntity : list) {
            workSlugList.add(llmUserWorkEntity.getWorkSlug());
        }

        return Result.success(workSlugList);
    }

    /**
     * 根据条件 分页 查询用户列表
     *
     * @param likeUserName 用户名模糊查询|admin
     * @param roleCodeList 角色列表|["admin","manager"]
     * @param workSlugList 工作区列表|["dwajdwaddwad1","dwaddwerwew2"]
     * @param openStatus   启用状态 -1:全部 0:启用 1:禁用|-1
     * @param page         当前页|1
     * @param pageSize     每页条数|10
     * @return
     */
    @Override
    public Result<PageResult<LlmUserInfoResponseVO>> findUserList(String likeUserName, List<String> roleCodeList, List<String> workSlugList, Integer openStatus, Integer page, Integer pageSize) {
        if(page < 0){
            page = 1;
        }
        if(pageSize < 0){
            pageSize = 10;
        }
        if(null != openStatus && (openStatus < -1 || openStatus > 1)){
            return Result.failure("启用/禁用状态参数异常!");
        }

        //构建查询条件 == 将所有的用户查询出来
        LambdaQueryWrapper<UserInfoEntity> userInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(likeUserName)){
            userInfoEntityLambdaQueryWrapper.like(UserInfoEntity::getUserName,likeUserName);
        }
        if(null != openStatus && openStatus != -1){
            //0-激活 1-注册待审核 2-审核未通过 3-禁用 4-注销 5-其他
            Integer status = openStatus == 0 ? 0 : 3;
            userInfoEntityLambdaQueryWrapper.eq(UserInfoEntity::getStatus,status);
        }
        //查询总数
        long total = userInfoService.count(userInfoEntityLambdaQueryWrapper);
        //进行分页查询
        Page<UserInfoEntity> pageParam = new PageDTO(page,pageSize,false);
        Page<UserInfoEntity> pageResult = userInfoService.page(pageParam, userInfoEntityLambdaQueryWrapper);
        List<UserInfoEntity> records = pageResult.getRecords();

        //获取这部分用户的信息角色，进一步查询关联的workSlug
        final Set<String> userNameSet = Optional.ofNullable(records).orElse(new ArrayList<>())
                .stream()
                .map(entity -> entity.getUserName())
                .collect(Collectors.toSet());
        //构建workSlug查询条件
        LambdaQueryWrapper<LlmUserWorkEntity> userWorkQueryWrapper = new LambdaQueryWrapper<>();
        userWorkQueryWrapper.in(LlmUserWorkEntity::getUserName,userNameSet);
        List<LlmUserWorkEntity> userWorkEntityList = llmUserWorkService.list(userWorkQueryWrapper);
        //提取用户名-工作区列表映射
        final Map<String, List<String>> userAndWorkSlugListMap = Optional.ofNullable(userWorkEntityList).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(entity -> entity.getUserName(), Collectors.mapping(entity -> entity.getWorkSlug(), Collectors.toList())));
        //调用
        if(CollectionUtils.isNotEmpty(roleCodeList)){

        }

        return null;
    }
}
