package com.hearyou.anything.manager.uaa;

import cn.dev33.satoken.stp.StpInterface;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hearyou.anything.manager.entity.SysRolePermissionEntity;
import com.hearyou.anything.manager.entity.SysUserRoleEntity;
import com.hearyou.anything.manager.service.SysRolePermissionService;
import com.hearyou.anything.manager.service.SysUserRoleService;
import com.hearyou.anything.manager.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 自定义llm的权限管理类
 */
@Slf4j
@Component
public class LlmStpInterface implements StpInterface {

    private UserInfoService userInfoService;

    private SysUserRoleService sysUserRoleService;

    private SysRolePermissionService sysRolePermissionService;

    @Autowired
    public LlmStpInterface(UserInfoService userInfoService, SysUserRoleService sysUserRoleService, SysRolePermissionService sysRolePermissionService) {
        this.userInfoService = userInfoService;
        this.sysUserRoleService = sysUserRoleService;
        this.sysRolePermissionService = sysRolePermissionService;
    }

    /**
     * 返回指定账号id所拥有的权限码集合
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 该账号id具有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        log.info("loginId:{}, loginType:{} == > getPermissionList", loginId, loginType);
        //查询用户的角色编码，再去查询角色对应的权限编码集合
        LambdaQueryWrapper<SysUserRoleEntity> userRoleQueryWrapper = new LambdaQueryWrapper<>();
        userRoleQueryWrapper.eq(SysUserRoleEntity::getUserName,loginId);
        SysUserRoleEntity userRoleEntity = sysUserRoleService.getOne(userRoleQueryWrapper);
        if(null == userRoleEntity){
            throw new RuntimeException("该用户没有角色,鉴权失败!");
        }

        final String roleCode = userRoleEntity.getRoleCode();
        LambdaQueryWrapper<SysRolePermissionEntity> rolePermissionEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        rolePermissionEntityLambdaQueryWrapper.eq(SysRolePermissionEntity::getRoleCode,roleCode);
        List<SysRolePermissionEntity> rolePermissionEntityList = sysRolePermissionService.list(rolePermissionEntityLambdaQueryWrapper);
        //提取权限列表
        final List<String> permissionCodeList = Optional.ofNullable(rolePermissionEntityList).orElse(new ArrayList<>()).stream().map(entity -> entity.getPermissionCode()).collect(Collectors.toList());
        return permissionCodeList;
    }

    /**
     * 返回指定账号id所拥有的角色标识集合
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 该账号id具有的角色标识集合
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        log.info("loginId:{}, loginType:{} == > getRoleList", loginId, loginType);
        LambdaQueryWrapper<SysUserRoleEntity> userRoleQueryWrapper = new LambdaQueryWrapper<>();
        userRoleQueryWrapper.eq(SysUserRoleEntity::getUserName,loginId);
        SysUserRoleEntity userRoleEntity = sysUserRoleService.getOne(userRoleQueryWrapper);
        return Arrays.asList(userRoleEntity.getRoleCode());
    }
}
