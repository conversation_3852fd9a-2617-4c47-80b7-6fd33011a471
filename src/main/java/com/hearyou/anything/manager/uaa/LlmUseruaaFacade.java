package com.hearyou.anything.manager.uaa;

import com.hearyou.anything.manager.vo.response.business.LlmUserInfoResponseVO;
import com.hearyou.anything.manager.vo.response.business.LlmUserLoginResponseVO;
import com.hearyou.common.base.PageResult;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.vo.request.business.LoginRequestVO;
import com.hearyou.anything.manager.vo.request.business.UserInfoRequestVO;
import java.util.List;

/**
 * 用户+认证 相关业务管理服务
 */
public interface LlmUseruaaFacade {


    /**
     * 创建llm用户 并 绑定角色和工作区权限
     * @param requestVO
     * @return
     */
    Result<Boolean> saveUserBind(UserInfoRequestVO requestVO);

    /**
     * 更新用户状态
     * @param username 用户名|admin
     * @param disabledStatus 禁用状态|true
     * @return
     */
    Result<Boolean> updateUserStatus(String username, Boolean disabledStatus);

    /**
     * 删除用户
     * @param username 用户名|admin
     * @return
     */
    Result<Boolean> deleteUser(String username);

    /**
     * 更新用户信息
     *
     * @param requestVO
     * @return
     */
    Result<Boolean> updateUserBind(UserInfoRequestVO requestVO);

    /**
     * 登录
     * @param requestVO
     * @return
     */
    Result<LlmUserLoginResponseVO> login(LoginRequestVO requestVO);

    /**
     * 根据用户名查询所属的工作区列表
     * @param username 用户名
     * @return
     */
    Result<List<String>> findWorkSlugs(String username);

    /**
     * 根据条件 分页 查询用户列表
     *
     * @param likeUserName 用户名模糊查询|admin
     * @param roleCodeList 角色列表|["admin","manager"]
     * @param workSlugList 工作区列表|["dwajdwaddwad1","dwaddwerwew2"]
     * @param openStatus   启用状态 -1:全部 0:启用 1:禁用|-1
     * @param page         当前页|1
     * @param pageSize     每页条数|10
     * @return
     */
    Result<PageResult<LlmUserInfoResponseVO>> findUserList(String likeUserName, List<String> roleCodeList, List<String> workSlugList, Integer openStatus, Integer page, Integer pageSize);
}
