package com.hearyou.anything.manager.exception;

public class CommonException extends RuntimeException {
    private Integer code;
    private String message;

    public Integer getCode() {
        return this.code;
    }

    public CommonException setCode(Integer code) {
        this.code = code;
        return this;
    }

    public CommonException setMessage(String message) {
        this.message = message;
        return this;
    }

    public String getMessage() {
        return this.message;
    }

    public CommonException() {
    }

    public CommonException(Integer code) {
        this.code = code;
    }

    public CommonException(String message) {
        super(message);
        this.message = message;
    }

    public CommonException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}