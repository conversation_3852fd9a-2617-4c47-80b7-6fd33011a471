package com.hearyou.anything.manager.enums;

import cn.hutool.core.util.StrUtil;
import java.util.Arrays;
import java.util.Optional;

/**
 * Llm的角色枚举类
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/9 19:03
 */
public enum LlmRoleEnum {
    DEFAULT("default","用户"),
    MANAGER("manager","管理员"),
    ADMINISTRATOR("admin","超级管理员");

    private String roleCode;

    private String roleName;

    LlmRoleEnum(String roleCode, String roleName) {
        this.roleCode = roleCode;
        this.roleName = roleName;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public static LlmRoleEnum getEnumByRoleCode(String roleCode) {
        if(StrUtil.isBlank(roleCode)){
            return null;
        }
        Optional<LlmRoleEnum> first = Arrays.stream(LlmRoleEnum.values()).filter(e -> e.getRoleCode().equals(roleCode)).findFirst();
        if(first.isPresent()){
            return first.get();
        }
        return null;
    }

    public static LlmRoleEnum getEnumByRoleName(String roleName) {
        if(StrUtil.isBlank(roleName)){
            return null;
        }
        Optional<LlmRoleEnum> first = Arrays.stream(LlmRoleEnum.values()).filter(e -> e.getRoleName().equals(roleName)).findFirst();
        if(first.isPresent()){
            return first.get();
        }
        return null;
    }
}
