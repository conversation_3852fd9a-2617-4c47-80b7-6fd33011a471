package com.hearyou.anything.manager.enums;

/**
 * 文件类型枚举
 */
public enum FileTypeEnum {
    
    PDF(".pdf", "application/pdf", "PDF文档"),
    DOCX(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "Word文档"),
    TXT(".txt", "text/plain", "文本文件"),
    MD(".md", "text/plain", "Markdown文件");
    
    private final String extension;
    private final String mimeType;
    private final String description;
    
    FileTypeEnum(String extension, String mimeType, String description) {
        this.extension = extension;
        this.mimeType = mimeType;
        this.description = description;
    }
    
    public String getExtension() {
        return extension;
    }
    
    public String getMimeType() {
        return mimeType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static FileTypeEnum getByExtension(String extension) {
        if (extension == null) {
            return null;
        }
        for (FileTypeEnum fileType : values()) {
            if (fileType.extension.equalsIgnoreCase(extension)) {
                return fileType;
            }
        }
        return null;
    }
    
    public static boolean isSupported(String extension) {
        return getByExtension(extension) != null;
    }
}