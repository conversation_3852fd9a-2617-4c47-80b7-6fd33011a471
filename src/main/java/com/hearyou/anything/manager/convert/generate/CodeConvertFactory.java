package com.hearyou.anything.manager.convert.generate;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hearyou.common.utils.SpringContextUtil;

import java.util.Date;

/**
 * 编码生成器
 */
public final class CodeConvertFactory {

    /**
     * code生成器
     * @param perfix 前缀
     * @return
     */
    public static String createCode(String perfix){
        return createCode(perfix,DatePattern.PURE_DATETIME_MS_PATTERN,5);
    }

    /**
     * code生成器-雪花
     * @param perfix 前缀
     * @return
     */
    public static String createCodeSnowflake(String perfix){
        StringBuilder builder = new StringBuilder();
        if(StrUtil.isNotBlank(perfix)){
            builder.append(perfix).append("-");
        }
        Snowflake snowflake = SpringContextUtil.getApplicationContext().getBean(Snowflake.class);
        builder.append(snowflake.nextIdStr());
        return builder.toString();
    }

    /**
     * code生成器
     * @param perfix 前缀
     * @param readomNum 随机数的位数
     * @return
     */
    public static String createCode(String perfix,int readomNum){
        return createCode(perfix,DatePattern.PURE_DATETIME_MS_PATTERN,readomNum);
    }

    /**
     * code生成器
     * @param perfix 前缀
     * @param dateFormat 时间格式，默认为 yyyyMMddHHmmssSSS
     * @param readomNum 随机数的位数
     * @return
     */
    public static String createCode(String perfix,String dateFormat,int readomNum){
        if (StrUtil.isBlank(dateFormat)){
            dateFormat = DatePattern.PURE_DATETIME_MS_PATTERN;
        }

        StringBuilder builder = new StringBuilder();
        if(StrUtil.isNotBlank(perfix)){
            builder.append(perfix).append("-");
        }

        builder.append(RandomUtil.randomNumbers(readomNum));
        builder.append(DateUtil.format(new Date(),dateFormat));
        return builder.toString();
    }

    /**
     * 生成redis的编码-前缀+固定位数seq
     */
    public static String generateCodeByRedis(String generateBeanName,String perfix,int sequenceLength){
        RedisCodeGenerate codeGenerate = SpringContextUtil.getBean(generateBeanName,RedisCodeGenerate.class);
        return codeGenerate.generateCode(perfix,sequenceLength);
    }

    /**
     * 生成redis的编码-前缀+日期+固定位数seq
     */
    public static String generateDateCodeByRedis(String generateBeanName,String perfix,int sequenceLength){
        RedisCodeGenerate codeGenerate = SpringContextUtil.getBean(generateBeanName,RedisCodeGenerate.class);
        return codeGenerate.generateDateCode(perfix,sequenceLength);
    }

    /**
     * 生成redis的编码-前缀+日期时间+固定位数seq
     */
    public static String generateDateTimeCodeByRedis(String generateBeanName,String perfix,int sequenceLength){
        RedisCodeGenerate codeGenerate = SpringContextUtil.getBean(generateBeanName,RedisCodeGenerate.class);
        return codeGenerate.generateDateTimeCode(perfix,sequenceLength);
    }
}
