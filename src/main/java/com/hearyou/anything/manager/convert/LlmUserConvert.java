package com.hearyou.anything.manager.convert;

import cn.dev33.satoken.stp.SaTokenInfo;
import com.hearyou.anything.manager.convert.generate.CodeConvertFactory;
import com.hearyou.anything.manager.entity.LlmUserWorkEntity;
import com.hearyou.anything.manager.entity.SysUserRoleEntity;
import com.hearyou.anything.manager.entity.UserInfoEntity;
import com.hearyou.anything.manager.enums.LlmRoleEnum;
import com.hearyou.anything.manager.vo.request.business.UserInfoRequestVO;
import com.hearyou.anything.manager.vo.request.llm.admin.LlmNewUserRequestVO;
import com.hearyou.anything.manager.vo.response.business.LlmUserLoginResponseVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,imports = {Date.class, CodeConvertFactory.class})
public interface LlmUserConvert extends BaseConvert{
    @Mapping(target = "role", source = "roleCode")
    LlmNewUserRequestVO saveUserRequestVO2LlmNewUserRequestVO(UserInfoRequestVO requestVO);

    @Mapping(target="userCreateType",expression = "java(0)")
    @Mapping(target = "status",expression = "java(0)")
    UserInfoEntity llmNewUserResponseVO2UserInfoEntity(UserInfoRequestVO requestVO, Integer llmUserId);

    @Mapping(target="linkCode",expression = "java(CodeConvertFactory.generateDateCodeByRedis(\"sysUserRoleRedisCodeGenerate\",\"UR\",6))")
    @Mapping(target="roleCode",expression = "java(roleEnum.getRoleCode())")
    SysUserRoleEntity userAndRole2SysUserRole(String userName, LlmRoleEnum roleEnum);

    LlmUserWorkEntity userAndWorkslug2LlmUserWork(String userName, String workSlug);

    default List<LlmUserWorkEntity> userAndWorkslugList2LlmUserWorkList(String userName, List<String> workSlugs){
        if(StringUtils.isBlank(userName)){
            throw new IllegalArgumentException("userName不能为空");
        }
        if(CollectionUtils.isEmpty(workSlugs)){
            throw new IllegalArgumentException("workSlugs不能为空");
        }
        List<LlmUserWorkEntity> llmUserWorkEntityList = new ArrayList<>(workSlugs.size());
        for (String workSlug : workSlugs) {
            LlmUserWorkEntity llmUserWorkEntity = userAndWorkslug2LlmUserWork(userName, workSlug);
            llmUserWorkEntityList.add(llmUserWorkEntity);
        }
        return llmUserWorkEntityList;
    }

    @Mapping(target = "expireTime",source = "tokenInfo.tokenTimeout")
    @Mapping(target = "token",source = "tokenInfo.tokenValue")
    LlmUserLoginResponseVO userinfoAndRolePermissionAndWorkSlugList2TokenInfo(UserInfoEntity userInfoEntity, String roleCode, List<String> permissionCodeList, List<String> workSlugList, SaTokenInfo tokenInfo);
}
