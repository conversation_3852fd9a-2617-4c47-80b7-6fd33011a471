package com.hearyou.anything.manager.convert;

import com.hearyou.anything.manager.convert.generate.CodeConvertFactory;
import com.hearyou.anything.manager.entity.LlmWorkspaceThreadEntity;
import com.hearyou.anything.manager.vo.request.llm.thread.AddWorkspaceThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.UpdateWorkspaceThreadRequestVO;
import com.hearyou.anything.manager.vo.response.llm.thread.LlmWorkSpaceThreadResponseVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;

import java.util.Date;
import java.util.List;

/**
 * 工作区线程转换器
 */
@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS, imports = {Date.class, CodeConvertFactory.class})
public interface LlmWorkSpaceThreadConvert {

    /**
     * 添加请求VO转实体
     * @param requestVO 添加请求VO
     * @return 工作区线程实体
     */
    @Mapping(target = "workspaceThreadCode", expression = "java(CodeConvertFactory.createCodeSnowflake(\"WT\"))")
    LlmWorkspaceThreadEntity convertLlmWorkspaceThreadEntity(AddWorkspaceThreadRequestVO requestVO);

    /**
     * 更新工作区线程实体
     * @param requestVO 更新请求参数
     * @param entity 目标实体
     */
    @Mapping(target = "workspaceThreadCode", ignore = true)
    void updateLlmWorkspaceThreadEntity(UpdateWorkspaceThreadRequestVO requestVO, @MappingTarget LlmWorkspaceThreadEntity entity);

    /**
     * 实体转响应VO
     * @param entity 工作区线程实体
     * @return 响应VO
     */
    LlmWorkSpaceThreadResponseVO convertToResponseVO(LlmWorkspaceThreadEntity entity);

    /**
     * 实体列表转响应VO列表
     * @param entities 工作区线程实体列表
     * @return 响应VO列表
     */
    List<LlmWorkSpaceThreadResponseVO> convertToResponseVOList(List<LlmWorkspaceThreadEntity> entities);
}
