package com.hearyou.anything.manager.convert.generate;

import java.util.List;

/**
 * redis编码生成器
 */
public interface RedisCodeGenerate {

    /**
     * 生成code
     * @return
     */
    String generateCode(int sequenceLength);

    /**
     * 生成code
     * @return
     */
    String generateCode(String perfix,int sequenceLength);

    /**
     * 生成code
     * @return
     */
    String generateCode(String perfix,String join,int sequenceLength);

    /**
     * 生成code
     * @return
     */
    String generateCode(String perfix,String join,String dateFormat,int sequenceLength);

    /**
     * 生成code
     * @param perfix
     * @param join
     * @param sequenceLength
     * @param randomLength
     * @return
     */
    String generateCode(String perfix, String join, int sequenceLength, int randomLength);

    /**
     * 生成code
     * @param perfix
     * @param join
     * @param sequenceLength
     * @param randomLength
     * @param readomJoin
     * @return
     */
    String generateCode(String perfix, String join, int sequenceLength, int randomLength,String readomJoin);

    /**
     * 生成code
     * @param perfix
     * @param join
     * @param dateStr 传入的格式化后的时间
     * @param sequenceLength
     * @param randomLength
     * @param readomJoin
     * @return
     */
    String generateCode(String perfix, String join,String dateStr,int sequenceLength, int randomLength,String readomJoin);

    /**
     * 通过 日期 + Redis序列号 批量生成编码
     */
    String generateDateCode(int sequenceLength);

    /**
     * 通过 日期 + Redis序列号 生成编码
     */
    String generateDateCode(String perfix,int sequenceLength);
    
    /**
     * 通过 日期 + Redis序列号 生成编码
     */
    String generateDateCode(String perfix,String join,int sequenceLength);

    /**
     * 通过 日期 + Redis序列号 + 随机数 生成编码
     */
    String generateDateCode(String perfix, String join, int sequenceLength, int randomLength);

    /**
     * 通过 日期 + Redis序列号 + 随机数 生成编码
     */
    String generateDateCode( String perfix, String join, int sequenceLength, int randomLength,String readomJoin);

    /**
     * 通过 日期 + 时间 + Redis序列号 生成编码
     */
    String generateDateTimeCode(int sequenceLength);

    /**
     * 通过 日期 + 时间 + Redis序列号 生成编码
     */
    String generateDateTimeCode(String perfix,int sequenceLength);

    /**
     * 通过 日期 + 时间 + Redis序列号 生成编码
     */
    String generateDateTimeCode(String perfix,String join,int sequenceLength);

    /**
     * 通过 日期 + 时间 + Redis序列号 + 随机数 生成编码
     */
    String generateDateTimeCode( String perfix, String join, int sequenceLength, int randomLength);

    /**
     * 通过 日期 + 时间 + Redis序列号 + 随机数 生成编码
     */
    String generateDateTimeCode(String perfix, String join, int sequenceLength, int randomLength,String readomJoin);

    /**
     * 通过 日期 + Redis序列号 批量生成编码
     */
    List<String> batchGenerateDateCode(int sequenceLength,int count);

    /**
     * 通过 日期 + Redis序列号 批量生成编码
     */
    List<String> batchGenerateDateCode(String perfix,int sequenceLength,int count);

    /**
     * 通过 日期 + Redis序列号 批量生成编码
     */
    List<String> batchGenerateDateCode(String perfix,String join,int sequenceLength,int count);

    /**
     * 通过 日期 + Redis序列号 + 随机数 批量生成编码
     */
    List<String> batchGenerateDateCode(String perfix, String join, int sequenceLength, int randomLength,int count);

    /**
     * 通过 日期 + Redis序列号 + 随机数 批量生成编码
     */
    List<String> batchGenerateDateCode( String perfix, String join, int sequenceLength, int randomLength,String readomJoin,int count);

    /**
     * 通过 日期 + 时间 + Redis序列号 批量生成编码
     */
    List<String> batchGenerateDateTimeCode(int sequenceLength,int count);

    /**
     * 通过 日期 + 时间 + Redis序列号 批量生成编码
     */
    List<String> batchGenerateDateTimeCode(String perfix,int sequenceLength,int count);

    /**
     * 通过 日期 + 时间 + Redis序列号 批量生成编码
     */
    List<String> batchGenerateDateTimeCode(String perfix,String join,int sequenceLength,int count);

    /**
     * 通过 日期 + 时间 + Redis序列号 + 随机数 批量生成编码
     */
    List<String> batchGenerateDateTimeCode( String perfix, String join, int sequenceLength, int randomLength,int count);

    /**
     * 通过 日期 + 时间 + Redis序列号 + 随机数 批量生成编码
     */
    List<String> batchGenerateDateTimeCode( String perfix, String join, int sequenceLength, int randomLength,String readomJoin,int count);
}
