package com.hearyou.anything.manager.convert.generate;

import com.hearyou.common.utils.SpringContextUtil;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 模块通用redis生成器
 */
public abstract class LlmRedisCodeGenerate extends RedisCodeGenerateImpl {

    @Override
    public String getRedisKeyFormat() {
        return "mems:maintenance:code:%s";
    }

    @Override
    public RedisTemplate<String, Object> getRedisTemplate() {
        return SpringContextUtil.getBean("codeGenerateRedisTemplate",RedisTemplate.class);
    }
}