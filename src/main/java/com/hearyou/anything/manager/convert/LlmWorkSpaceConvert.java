package com.hearyou.anything.manager.convert;


import com.hearyou.anything.manager.convert.generate.CodeConvertFactory;
import com.hearyou.anything.manager.entity.LlmWorkspaceEntity;
import com.hearyou.anything.manager.vo.request.llm.work.AddWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.UpdateWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.response.llm.work.LlmWorkSpaceResponseVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;

import java.util.Date;
import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,imports = {Date.class, CodeConvertFactory.class})
public interface LlmWorkSpaceConvert {


    @Mapping(target = "workspaceCode",expression = "java(CodeConvertFactory.createCodeSnowflake(\"W\"))")
    @Mapping(target = "whetherEnable",expression = "java(1)")
    @Mapping(target = "workspaceName",source = "name")
    LlmWorkspaceEntity convertLlmWorkspaceEntity(AddWorkspaceRequestVO requestVO);

    /**
     * 更新工作区实体
     * @param requestVO 更新请求参数
     * @param entity 目标实体
     */
    @Mapping(target = "workspaceCode", ignore = true)
    @Mapping(target = "whetherEnable", ignore = true)
    void updateLlmWorkspaceEntity(UpdateWorkspaceRequestVO requestVO, @MappingTarget LlmWorkspaceEntity entity);

    /**
     * 实体转响应VO
     * @param entity 工作区实体
     * @return 响应VO
     */
    LlmWorkSpaceResponseVO convertToResponseVO(LlmWorkspaceEntity entity);

    /**
     * 实体列表转响应VO列表
     * @param entities 工作区实体列表
     * @return 响应VO列表
     */
    List<LlmWorkSpaceResponseVO> convertToResponseVOList(List<LlmWorkspaceEntity> entities);
}
