package com.hearyou.anything.manager.convert.generate;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.data.redis.core.RedisTemplate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

public abstract class RedisCodeGenerateImpl implements RedisCodeGenerate {

    public final String EMPTY_STRING = "";

    public final Long  DATE_EXPIRE_MILLISECONDS = 24 * 60 * 60 * 1000L;

    public final Long  DATE_TIME_EXPIRE_MILLISECONDS = 1000L;

    public abstract String getRedisKeyFormat();

    public abstract String getSeqKey();

    public abstract RedisTemplate<String, Object> getRedisTemplate();

    @Override
    public String generateCode(int sequenceLength) {
        return generateCode(EMPTY_STRING,EMPTY_STRING,EMPTY_STRING,sequenceLength,0,EMPTY_STRING);
    }

    @Override
    public String generateCode(String perfix, int sequenceLength) {
        return generateCode(perfix,EMPTY_STRING,EMPTY_STRING,sequenceLength,0,EMPTY_STRING);
    }

    @Override
    public String generateCode(String perfix, String join, int sequenceLength) {
        return generateCode(perfix,join,EMPTY_STRING,sequenceLength,0,EMPTY_STRING);
    }

    @Override
    public String generateCode(String perfix, String join, String dateFormat, int sequenceLength) {
        String formatDateStr = EMPTY_STRING;
        if(StrUtil.isNotBlank(dateFormat)){
            formatDateStr = DateUtil.format(new Date(), dateFormat);
        }
        return generateCode(perfix,join,formatDateStr,sequenceLength,0,EMPTY_STRING);
    }

    @Override
    public String generateCode(String perfix, String join, int sequenceLength, int randomLength) {
        return generateCode(perfix,join,EMPTY_STRING,sequenceLength,randomLength,EMPTY_STRING);
    }

    @Override
    public String generateCode(String perfix, String join, int sequenceLength, int randomLength, String readomJoin) {
        return generateCode(perfix, join,EMPTY_STRING, sequenceLength, randomLength,EMPTY_STRING);
    }

    /**
     * 最终的默认实现
     * @param perfix
     * @param join
     * @param dateStr 传入的格式化后的时间
     * @param sequenceLength
     * @param randomLength
     * @param readomJoin
     * @return
     */
    @Override
    public String generateCode(String perfix, String join, String dateStr, int sequenceLength, int randomLength, String readomJoin) {
        return generateCodeAndSetExpireTime(perfix, join, dateStr, sequenceLength, randomLength, readomJoin,0);
    }

    @Override
    public String generateDateCode(int sequenceLength) {
        return generateDateCode(EMPTY_STRING, EMPTY_STRING,sequenceLength,0,EMPTY_STRING);
    }

    @Override
    public String generateDateCode(String perfix, int sequenceLength) {
        return generateDateCode(perfix , EMPTY_STRING , sequenceLength, 0, EMPTY_STRING);
    }

    @Override
    public String generateDateCode(String perfix, String join, int sequenceLength) {
        return generateDateCode(perfix,join,sequenceLength,0,EMPTY_STRING);
    }

    @Override
    public String generateDateCode(String perfix, String join, int sequenceLength, int randomLength) {
        return generateDateCode(perfix,join,sequenceLength,randomLength,EMPTY_STRING);
    }

    @Override
    public String generateDateCode(String perfix, String join, int sequenceLength, int randomLength, String readomJoin) {
        //生成 日期格式的时间
        final String dateStr = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        return generateCodeAndSetExpireTime(perfix, join, dateStr, sequenceLength, randomLength, readomJoin, DATE_EXPIRE_MILLISECONDS);
    }

    @Override
    public String generateDateTimeCode(int sequenceLength) {
        return generateDateTimeCode(EMPTY_STRING, EMPTY_STRING,sequenceLength,0,EMPTY_STRING);
    }

    @Override
    public String generateDateTimeCode(String perfix, int sequenceLength) {
        return generateDateTimeCode(perfix , EMPTY_STRING , sequenceLength, 0, EMPTY_STRING);
    }

    @Override
    public String generateDateTimeCode(String perfix, String join, int sequenceLength) {
        return generateDateTimeCode(perfix,join,sequenceLength,0,EMPTY_STRING);
    }

    @Override
    public String generateDateTimeCode(String perfix, String join, int sequenceLength, int randomLength) {
        return generateDateTimeCode(perfix,join,sequenceLength,randomLength,EMPTY_STRING);
    }

    @Override
    public String generateDateTimeCode(String perfix, String join, int sequenceLength, int randomLength, String readomJoin) {
        //生成 日期格式的时间
        String dateStr = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        return generateCodeAndSetExpireTime(perfix, join, dateStr, sequenceLength, randomLength, readomJoin,DATE_TIME_EXPIRE_MILLISECONDS);
    }

    /**
     * 通过 日期 + Redis序列号 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateCode(int sequenceLength, int count) {
        return batchGenerateDateCode(null,EMPTY_STRING,sequenceLength, 0, EMPTY_STRING,count);
    }

    /**
     * 通过 日期 + Redis序列号 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateCode(String perfix, int sequenceLength, int count) {
        return batchGenerateDateCode(perfix,EMPTY_STRING,sequenceLength,0,EMPTY_STRING,count);
    }

    /**
     * 通过 日期 + Redis序列号 + 随机数 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateCode(String perfix, String join, int sequenceLength,int count) {
        return batchGenerateDateCode(perfix,join,sequenceLength,0,EMPTY_STRING,count);
    }

    /**
     * 通过 日期 + Redis序列号 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateCode(String perfix, String join, int sequenceLength, int randomLength, int count) {
        return batchGenerateDateCode(perfix,join,sequenceLength,randomLength,EMPTY_STRING,count);
    }

    /**
     * 通过 日期 + Redis序列号 + 随机数 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateCode(String perfix, String join, int sequenceLength, int randomLength, String readomJoin, int count) {
        //生成 日期格式的时间
        final String dateStr = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        return batchGenerateCodeAndSetExpireTime(perfix, join, dateStr, sequenceLength, randomLength, readomJoin, DATE_EXPIRE_MILLISECONDS,count);
    }

    /**
     * 通过 日期 + 时间 + Redis序列号 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateTimeCode(int sequenceLength, int count) {
        return batchGenerateDateTimeCode(null, EMPTY_STRING, sequenceLength, 0, EMPTY_STRING,count);
    }

    /**
     * 通过 日期 + 时间 + Redis序列号 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateTimeCode(String perfix, int sequenceLength, int count) {
        return batchGenerateDateTimeCode(perfix,EMPTY_STRING,sequenceLength, 0, EMPTY_STRING,count);
    }

    /**
     * 通过 日期 + 时间 + Redis序列号 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateTimeCode(String perfix, String join, int sequenceLength, int randomLength, int count) {
        return batchGenerateDateTimeCode(perfix, join,sequenceLength, randomLength, EMPTY_STRING, count);
    }

    /**
     * 通过 日期 + 时间 + Redis序列号 + 随机数 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateTimeCode(String perfix, String join, int sequenceLength, int count) {
        return batchGenerateDateTimeCode(perfix, join, sequenceLength, 0,EMPTY_STRING, count);
    }

    /**
     * 通过 日期 + 时间 + Redis序列号 + 随机数 批量生成编码
     */
    @Override
    public List<String> batchGenerateDateTimeCode(String perfix, String join, int sequenceLength, int randomLength, String readomJoin, int count) {
        //生成 日期格式的时间
        String dateStr = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        return batchGenerateCodeAndSetExpireTime(perfix, join, dateStr, sequenceLength, randomLength, readomJoin,DATE_TIME_EXPIRE_MILLISECONDS,count);
    }

    /**
     * 算出来对应位数下的最大整数
     * @param digits
     * @return
     */
    protected int calculateMaxValueForDigits(int digits) {
        if (digits <= 0) {
            throw new IllegalArgumentException("Digits should be a positive integer");
        }

        int maxValue = 1;
        for (int i = 0; i < digits; i ++) {
            maxValue *= 10;
        }
        return maxValue - 1;
    }

    /**
     * 生成redis序列的key
     * @param redisKeyFormat
     * @param seqKey
     * @param dateStr
     * @return
     */
    protected String genRedisKey(String redisKeyFormat, String seqKey, String dateStr){
        if(StrUtil.isNotBlank(dateStr)){
            return String.format(redisKeyFormat,seqKey + ":" + dateStr);
        }
        return String.format(redisKeyFormat,seqKey);
    }

    /**
     * 获取redis的序列值 + 设置过期时间
     * @param redisSeqKey 序列的key
     * @param incrementValue 自增的值
     * @param expireMilliseconds 多少毫秒过期
     * @return
     */
    protected synchronized Long getSeqValueAndSetExpireTime(String redisSeqKey,long incrementValue,long expireMilliseconds){
        Long incValue = getRedisTemplate().opsForValue().increment(redisSeqKey, incrementValue);
        if(expireMilliseconds > 0){
            getRedisTemplate().expire(redisSeqKey,expireMilliseconds, TimeUnit.MILLISECONDS);
        }
        return incValue;
    }

    /**
     * 最终执行的方法
     * @param perfix
     * @param join
     * @param dateStr
     * @param sequenceLength
     * @param randomLength
     * @param readomJoin
     * @return
     */
    protected String generateCodeAndSetExpireTime(String perfix, String join, String dateStr, int sequenceLength, int randomLength, String readomJoin,long expireMilliseconds){
        if(StrUtil.isBlank(getSeqKey()) || StrUtil.isBlank(getRedisKeyFormat()) || null == getRedisTemplate()) {
            //如果这两个没有实现，是不行的
            throw new IllegalArgumentException("方法getSeqKey不能空实现/方法getRedisKeyFormat不能空实现/方法getRedisTemplate不能空实现");
        }

        //给两个join默认值
        if(StrUtil.isBlank(join)){
            join = EMPTY_STRING;
        }
        if (StrUtil.isBlank(readomJoin)){
            readomJoin = EMPTY_STRING;
        }

        //开始拼接
        StringBuilder builder = new StringBuilder();
        if(StrUtil.isNotBlank(perfix)){
            builder.append(perfix).append(join);
        }
        if(StrUtil.isNotBlank(dateStr)){
            builder.append(dateStr);
        }

        //生成redisKey --需要注意的是，有木有时间字段传递进来，有时间，需要seqkey + : dateStr
        final String redisKey = genRedisKey(getRedisKeyFormat(),getSeqKey(),dateStr);
        //获取自增的值，并且设置过期时间
        Long incValue = getSeqValueAndSetExpireTime(redisKey,1,expireMilliseconds);
        //序列格式化位数，如果超过了暂时允许，不超过的高位自动补位0
        if(sequenceLength > 0){
            //代表要生成对应位数的序列，先获取对应位数，再去格式化
            String strValue = String.valueOf(incValue);
            int length = strValue.length();
            if(length < sequenceLength){
                strValue = String.format("%0" + sequenceLength + "d", incValue);
            }
            builder.append(strValue);
        }

        //如果允许随机值
        if(randomLength > 0){
            //需要计算出来随机数的范围
            int maxRandomNum = calculateMaxValueForDigits(randomLength);
            //生成的范围需要包括最大值，因为后面的值不包含，所以需要max + 1
            int randomInt = RandomUtil.randomInt(1, maxRandomNum + 1);
            //进行格式化，不足的补0
            String randomFormat = String.format("%0" + randomLength + "d", randomInt);
            builder.append(readomJoin).append(randomFormat);
        }
        return builder.toString();
    }

    private List<String> batchGenerateCodeAndSetExpireTime(String perfix, String join, String dateStr, int sequenceLength, int randomLength, String readomJoin, long expireMilliseconds, int count) {
        if(StrUtil.isBlank(getSeqKey()) || StrUtil.isBlank(getRedisKeyFormat()) || null == getRedisTemplate()) {
            //如果这两个没有实现，是不行的
            throw new IllegalArgumentException("方法getSeqKey不能空实现/方法getRedisKeyFormat不能空实现/方法getRedisTemplate不能空实现");
        }
        if(count <= 0){
            throw new IllegalArgumentException("批量生成code失败!生成条数不能小于1!");
        }

        //给两个join默认值
        if(StrUtil.isBlank(join)){
            join = EMPTY_STRING;
        }
        if (StrUtil.isBlank(readomJoin)){
            readomJoin = EMPTY_STRING;
        }

        //生成redisKey --需要注意的是，有木有时间字段传递进来，有时间，需要seqkey + : dateStr
        final String redisKey = genRedisKey(getRedisKeyFormat(),getSeqKey(),dateStr);
        //获取自增的值，并且设置过期时间
        Long incValue = getSeqValueAndSetExpireTime(redisKey,count,expireMilliseconds);
        if (incValue == null) {
            throw new RuntimeException("redis increment命令执行失败");
        }
        //循环对应的id
        long startValue = incValue - Long.parseLong(String.valueOf(count));
        List<String> batchCodeList = new ArrayList<>(count);
        for (long i = startValue; i <= incValue; i ++) {
            //开始拼接
            StringBuilder builder = new StringBuilder();
            if(StrUtil.isNotBlank(perfix)){
                builder.append(perfix).append(join);
            }
            if(StrUtil.isNotBlank(dateStr)){
                builder.append(dateStr);
            }

            //序列格式化位数，如果超过了暂时允许，不超过的高位自动补位0
            if(sequenceLength > 0){
                //代表要生成对应位数的序列，先获取对应位数，再去格式化
                String strValue = String.valueOf(incValue);
                int length = strValue.length();
                if(length < sequenceLength){
                    strValue = String.format("%0" + sequenceLength + "d", incValue);
                }
                builder.append(strValue);
            }

            //如果允许随机值
            if(randomLength > 0){
                //需要计算出来随机数的范围
                int maxRandomNum = calculateMaxValueForDigits(randomLength);
                //生成的范围需要包括最大值，因为后面的值不包含，所以需要max + 1
                int randomInt = RandomUtil.randomInt(1, maxRandomNum + 1);
                //进行格式化，不足的补0
                String randomFormat = String.format("%0" + randomLength + "d", randomInt);
                builder.append(readomJoin).append(randomFormat);
            }
            batchCodeList.add(builder.toString());
        }

        return batchCodeList;
    }
}
