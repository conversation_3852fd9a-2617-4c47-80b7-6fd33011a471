package com.hearyou.anything.manager.convert;


import com.hearyou.anything.manager.convert.generate.CodeConvertFactory;
import com.hearyou.anything.manager.entity.LlmWorkFileEntity;
import com.hearyou.anything.manager.vo.request.llm.document.LlmWorkFileRequestVO;
import com.hearyou.anything.manager.vo.response.document.LlmWorkFileResponseVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

import java.util.Date;
import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,imports = {Date.class, CodeConvertFactory.class})
public interface DocumentConvert extends BaseConvert{


    @Mapping(target = "fileCode",expression = "java(CodeConvertFactory.createCodeSnowflake(\"F\"))")
    @Mapping(target = "businessType",expression = "java(\"llm\")")
    @Mapping(target = "whetherFolder",expression = "java(false)")
    @Mapping(target = "whetherEnable",expression = "java(0)")
    LlmWorkFileEntity convertLlmWorkFileEntity(LlmWorkFileRequestVO requestVO);

    @Mapping(target = "fileCode",expression = "java(CodeConvertFactory.createCodeSnowflake(\"F\"))")
    @Mapping(target = "businessType",expression = "java(\"llm\")")
    @Mapping(target = "whetherFolder",expression = "java(true)")
    @Mapping(target = "whetherEnable",expression = "java(0)")
    LlmWorkFileEntity convertFolderEntity(LlmWorkFileRequestVO requestVO);




    LlmWorkFileResponseVO convertLlmWorkFileResponseVO(LlmWorkFileEntity entity);


    List<LlmWorkFileResponseVO> convertLlmWorkFileResponseVOList(List<LlmWorkFileEntity> entityList);
}
