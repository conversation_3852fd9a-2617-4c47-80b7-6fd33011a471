package com.hearyou.anything.manager.facade;

import com.hearyou.anything.manager.vo.request.llm.work.AddWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.UpdateWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.WorkspaceQueryRequestVO;
import com.hearyou.anything.manager.vo.response.llm.work.LlmWorkSpaceResponseVO;
import com.hearyou.common.base.Result;

import java.util.List;

public interface LlmWorkSpaceFacade {


    /**
     * 校验工作区名称是否重复
     * @param name
     * @return
     */
    Result<Boolean> checkWorkspaceName(String name);


    /**
     * 校验工作区下是否有线程
     * @param workSlug
     * @return
     */
    Result<Boolean> checkWorkThreadDelete(String workSlug);

    /**
     * 添加工作区
     * @param vo
     * @return
     */
    Result<String> addWorkspace(AddWorkspaceRequestVO vo);

    /**
     * 更新工作区
     * @param requestVO 更新请求参数
     * @return 更新结果
     */
    Result<String> updateWorkspace(UpdateWorkspaceRequestVO requestVO);

    /**
     * 删除工作区
     * @param workspaceCode 工作区编码
     * @return 删除结果
     */
    Result<String> deleteWorkspace(String workspaceCode);

    /**
     * 根据编码查询工作区详情
     * @param workspaceCode 工作区编码
     * @return 工作区详情
     */
    Result<LlmWorkSpaceResponseVO> getWorkspaceByCode(String workspaceCode);

    /**
     * 查询工作区列表
     * @param queryVO 查询条件
     * @return 工作区列表
     */
    Result<List<LlmWorkSpaceResponseVO>> getWorkspaceList(WorkspaceQueryRequestVO queryVO);
}