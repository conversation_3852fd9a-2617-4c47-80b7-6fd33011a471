package com.hearyou.anything.manager.facade.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hearyou.anything.manager.convert.LlmWorkSpaceConvert;
import com.hearyou.anything.manager.entity.LlmWorkspaceEntity;
import com.hearyou.anything.manager.facade.LlmWorkspaceFacade;
import com.hearyou.anything.manager.service.LlmWorkspaceService;
import com.hearyou.anything.manager.vo.request.llm.work.AddWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.UpdateWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.WorkspaceQueryRequestVO;
import com.hearyou.anything.manager.vo.response.llm.work.LlmWorkSpaceResponseVO;
import com.hearyou.common.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class LlmWorkspaceFacadeImpl implements LlmWorkspaceFacade {


    @Autowired
    private LlmWorkspaceService llmWorkspaceService;

    @Autowired
    private LlmWorkSpaceConvert llmWorkSpaceConvert;


    /**
     * 添加工作区到数据库中
     * @param requestVO
     * @return
     */
    @Override
    public Result<String> addWorkspace(AddWorkspaceRequestVO requestVO) {

        String name = requestVO.getName();

        if (StrUtil.isBlank(name)){
            return Result.failure("添加失败，名称不能为空");
        }



        QueryWrapper<LlmWorkspaceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("workspace_name",name);

        long count = llmWorkspaceService.count(wrapper);

        if (count > 0){
            return Result.failure("添加失败，工作区名称重复");
        }

        LlmWorkspaceEntity llmWorkspaceEntity = llmWorkSpaceConvert.convertLlmWorkspaceEntity(requestVO);

        llmWorkspaceService.save(llmWorkspaceEntity);


        return Result.success("添加成功");
    }

    /**
     * 更新工作区
     * @param requestVO 更新请求参数
     * @return 更新结果
     */
    @Override
    public Result<String> updateWorkspace(UpdateWorkspaceRequestVO requestVO) {

        String workspaceCode = requestVO.getWorkspaceCode();

        if (StrUtil.isBlank(workspaceCode)) {
            return Result.failure("更新失败，工作区编码不能为空");
        }

        String name = requestVO.getName();
        if (StrUtil.isBlank(name)) {
            return Result.failure("更新失败，名称不能为空");
        }

        // 校验工作区是否存在
        QueryWrapper<LlmWorkspaceEntity> existWrapper = new QueryWrapper<>();
        existWrapper.eq("workspace_code", workspaceCode);
        LlmWorkspaceEntity existEntity = llmWorkspaceService.getOne(existWrapper);

        if (existEntity == null) {
            return Result.failure("更新失败，工作区不存在");
        }

        // 校验更新后的名称是否与其他工作区重名（排除当前工作区）
        QueryWrapper<LlmWorkspaceEntity> nameWrapper = new QueryWrapper<>();
        nameWrapper.eq("workspace_name", name);
        nameWrapper.ne("workspace_code", workspaceCode);

        long count = llmWorkspaceService.count(nameWrapper);
        if (count > 0) {
            return Result.failure("更新失败，工作区名称重复");
        }

        // 使用转换器更新实体
        llmWorkSpaceConvert.updateLlmWorkspaceEntity(requestVO, existEntity);

        llmWorkspaceService.updateById(existEntity);

        return Result.success("更新成功");
    }

    /**
     * 删除工作区
     * @param workspaceCode 工作区编码
     * @return 删除结果
     */
    @Override
    public Result<String> deleteWorkspace(String workspaceCode) {
        if (StrUtil.isBlank(workspaceCode)) {
            return Result.failure("删除失败，工作区编码不能为空");
        }

        // 校验工作区是否存在
        QueryWrapper<LlmWorkspaceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("workspace_code", workspaceCode);
        LlmWorkspaceEntity entity = llmWorkspaceService.getOne(wrapper);

        if (entity == null) {
            return Result.failure("删除失败，工作区不存在");
        }

        llmWorkspaceService.removeById(entity.getId());

        return Result.success("删除成功");
    }

    /**
     * 根据编码查询工作区详情
     * @param workspaceCode 工作区编码
     * @return 工作区详情
     */
    @Override
    public Result<LlmWorkSpaceResponseVO> getWorkspaceByCode(String workspaceCode) {
        if (StrUtil.isBlank(workspaceCode)) {
            return Result.failure("查询失败，工作区编码不能为空");
        }

        QueryWrapper<LlmWorkspaceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("workspace_code", workspaceCode);
        LlmWorkspaceEntity entity = llmWorkspaceService.getOne(wrapper);

        if (entity == null) {
            return Result.failure("查询失败，工作区不存在");
        }

        LlmWorkSpaceResponseVO responseVO = llmWorkSpaceConvert.convertToResponseVO(entity);

        return Result.success(responseVO);
    }

    /**
     * 查询工作区列表
     * @param queryVO 查询条件
     * @return 工作区列表
     */
    @Override
    public Result<List<LlmWorkSpaceResponseVO>> getWorkspaceList(WorkspaceQueryRequestVO queryVO) {
        QueryWrapper<LlmWorkspaceEntity> wrapper = new QueryWrapper<>();
        

            // 工作区名称模糊查询
            if (StrUtil.isNotBlank(queryVO.getWorkspaceName())) {
                wrapper.like("workspace_name", queryVO.getWorkspaceName());
            }
            
            // 工作区slug集合IN查询
            if (CollectionUtil.isNotEmpty(queryVO.getWorkspaceSlugs())) {
                wrapper.in("workspace_slug", queryVO.getWorkspaceSlugs());
            }
            
            // 是否启用状态精确查询
            if (queryVO.getWhetherEnable() != null) {
                wrapper.eq("whether_enable", queryVO.getWhetherEnable());
            }
        
        // 按创建时间倒序排列
        wrapper.orderByDesc("create_time");

        List<LlmWorkspaceEntity> entities = llmWorkspaceService.list(wrapper);
        List<LlmWorkSpaceResponseVO> responseVOList = llmWorkSpaceConvert.convertToResponseVOList(entities);

        return Result.success(responseVOList);
    }
}
