package com.hearyou.anything.manager.facade.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hearyou.anything.manager.convert.LlmWorkSpaceThreadConvert;
import com.hearyou.anything.manager.entity.LlmWorkspaceThreadEntity;
import com.hearyou.anything.manager.facade.LlmWorkSpaceThreadFacade;
import com.hearyou.anything.manager.service.LlmWorkspaceThreadService;
import com.hearyou.anything.manager.vo.request.llm.thread.AddWorkspaceThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.UpdateWorkspaceThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.WorkspaceThreadQueryRequestVO;
import com.hearyou.anything.manager.vo.response.llm.thread.LlmWorkSpaceThreadResponseVO;
import com.hearyou.common.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class LlmWorkSpaceThreadFacadeImpl implements LlmWorkSpaceThreadFacade {

    @Autowired
    private LlmWorkspaceThreadService llmWorkspaceThreadService;

    @Autowired
    private LlmWorkSpaceThreadConvert llmWorkSpaceThreadConvert;


    /**
     * 校验工作区下线程名称是否重复
     * @param workSlug
     * @param name
     * @return
     */
    @Override
    public Result<Boolean> checkWorkThreadName(String workSlug, String name) {

        if (StrUtil.isBlank(workSlug)){
            return Result.failure("校验失败，工作区slug不能为空");
        }

        if (StrUtil.isBlank(name)){
            return Result.failure("校验失败，线程名称不能为空");
        }

        QueryWrapper<LlmWorkspaceThreadEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("workspace_thread_slug", workSlug);
        wrapper.eq("workspace_thread_name",name);

        if (llmWorkspaceThreadService.count(wrapper)>0){
            return Result.success(false);
        }


        return Result.success(true);
    }

    /**
     * 添加工作区线程到数据库中
     * @param requestVO 添加请求参数
     * @return 添加结果
     */
    @Override
    public Result<String> addWorkspaceThread(AddWorkspaceThreadRequestVO requestVO) {

        String workspaceThreadName = requestVO.getWorkspaceThreadName();

        if (StrUtil.isBlank(workspaceThreadName)) {
            return Result.failure("添加失败，工作区线程名称不能为空");
        }

        String userName = requestVO.getUserName();
        if (StrUtil.isBlank(userName)) {
            return Result.failure("添加失败，用户名不能为空");
        }

        String workspaceSlug = requestVO.getWorkspaceSlug();
        if (StrUtil.isBlank(workspaceSlug)) {
            return Result.failure("添加失败，工作区slug不能为空");
        }

        // 校验同一工作区下线程名称是否重复
        QueryWrapper<LlmWorkspaceThreadEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("workspace_thread_name", workspaceThreadName);
        wrapper.eq("workspace_slug", workspaceSlug);

        long count = llmWorkspaceThreadService.count(wrapper);

        if (count > 0) {
            return Result.failure("添加失败，该工作区下线程名称重复");
        }

        LlmWorkspaceThreadEntity llmWorkspaceThreadEntity = llmWorkSpaceThreadConvert.convertLlmWorkspaceThreadEntity(requestVO);

        llmWorkspaceThreadService.save(llmWorkspaceThreadEntity);

        return Result.success("添加成功");
    }

    /**
     * 更新工作区线程
     * @param requestVO 更新请求参数
     * @return 更新结果
     */
    @Override
    public Result<String> updateWorkspaceThread(UpdateWorkspaceThreadRequestVO requestVO) {

        String workspaceThreadCode = requestVO.getWorkspaceThreadCode();

        if (StrUtil.isBlank(workspaceThreadCode)) {
            return Result.failure("更新失败，工作区线程编码不能为空");
        }

        String workspaceThreadName = requestVO.getWorkspaceThreadName();
        if (StrUtil.isBlank(workspaceThreadName)) {
            return Result.failure("更新失败，工作区线程名称不能为空");
        }

        // 校验工作区线程是否存在
        QueryWrapper<LlmWorkspaceThreadEntity> existWrapper = new QueryWrapper<>();
        existWrapper.eq("workspace_thread_code", workspaceThreadCode);
        LlmWorkspaceThreadEntity existEntity = llmWorkspaceThreadService.getOne(existWrapper);

        if (existEntity == null) {
            return Result.failure("更新失败，工作区线程不存在");
        }

        // 校验更新后的名称是否与同一工作区下其他线程重名（排除当前线程）
        QueryWrapper<LlmWorkspaceThreadEntity> nameWrapper = new QueryWrapper<>();
        nameWrapper.eq("workspace_thread_name", workspaceThreadName);
        nameWrapper.eq("workspace_slug", existEntity.getWorkspaceSlug());
        nameWrapper.ne("workspace_thread_code", workspaceThreadCode);

        long count = llmWorkspaceThreadService.count(nameWrapper);
        if (count > 0) {
            return Result.failure("更新失败，该工作区下线程名称重复");
        }

        // 使用转换器更新实体
        llmWorkSpaceThreadConvert.updateLlmWorkspaceThreadEntity(requestVO, existEntity);

        llmWorkspaceThreadService.updateById(existEntity);

        return Result.success("更新成功");
    }

    /**
     * 删除工作区线程
     * @param workspaceThreadCode 工作区线程编码
     * @return 删除结果
     */
    @Override
    public Result<String> deleteWorkspaceThread(String workspaceThreadCode) {
        if (StrUtil.isBlank(workspaceThreadCode)) {
            return Result.failure("删除失败，工作区线程编码不能为空");
        }

        // 校验工作区线程是否存在
        QueryWrapper<LlmWorkspaceThreadEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("workspace_thread_code", workspaceThreadCode);
        LlmWorkspaceThreadEntity entity = llmWorkspaceThreadService.getOne(wrapper);

        if (entity == null) {
            return Result.failure("删除失败，工作区线程不存在");
        }

        llmWorkspaceThreadService.removeById(entity.getId());

        return Result.success("删除成功");
    }

    /**
     * 根据编码查询工作区线程详情
     * @param workspaceThreadCode 工作区线程编码
     * @return 工作区线程详情
     */
    @Override
    public Result<LlmWorkSpaceThreadResponseVO> getWorkspaceThreadByCode(String workspaceThreadCode) {
        if (StrUtil.isBlank(workspaceThreadCode)) {
            return Result.failure("查询失败，工作区线程编码不能为空");
        }

        QueryWrapper<LlmWorkspaceThreadEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("workspace_thread_code", workspaceThreadCode);
        LlmWorkspaceThreadEntity entity = llmWorkspaceThreadService.getOne(wrapper);

        if (entity == null) {
            return Result.failure("查询失败，工作区线程不存在");
        }

        LlmWorkSpaceThreadResponseVO responseVO = llmWorkSpaceThreadConvert.convertToResponseVO(entity);

        return Result.success(responseVO);
    }

    /**
     * 查询工作区线程列表
     * @param queryVO 查询条件
     * @return 工作区线程列表
     */
    @Override
    public Result<List<LlmWorkSpaceThreadResponseVO>> getWorkspaceThreadList(WorkspaceThreadQueryRequestVO queryVO) {
        QueryWrapper<LlmWorkspaceThreadEntity> wrapper = new QueryWrapper<>();

        // 工作区线程名称模糊查询
        if (StrUtil.isNotBlank(queryVO.getWorkspaceThreadName())) {
            wrapper.like("workspace_thread_name", queryVO.getWorkspaceThreadName());
        }

        // 工作区线程slug集合IN查询
        if (CollectionUtil.isNotEmpty(queryVO.getWorkspaceThreadSlugs())) {
            wrapper.in("workspace_thread_slug", queryVO.getWorkspaceThreadSlugs());
        }

        // 用户名精确查询
        if (StrUtil.isNotBlank(queryVO.getUserName())) {
            wrapper.eq("user_name", queryVO.getUserName());
        }

        // 工作区slug精确查询
        if (StrUtil.isNotBlank(queryVO.getWorkspaceSlug())) {
            wrapper.eq("workspace_slug", queryVO.getWorkspaceSlug());
        }

        // 工作区slug集合IN查询
        if (CollectionUtil.isNotEmpty(queryVO.getWorkspaceSlugs())) {
            wrapper.in("workspace_slug", queryVO.getWorkspaceSlugs());
        }

        // 按创建时间倒序排列
        wrapper.orderByDesc("create_time");

        List<LlmWorkspaceThreadEntity> entities = llmWorkspaceThreadService.list(wrapper);
        List<LlmWorkSpaceThreadResponseVO> responseVOList = llmWorkSpaceThreadConvert.convertToResponseVOList(entities);

        return Result.success(responseVOList);
    }
}
