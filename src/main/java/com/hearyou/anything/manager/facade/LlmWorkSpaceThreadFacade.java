package com.hearyou.anything.manager.facade;

import com.hearyou.anything.manager.vo.request.llm.thread.AddWorkspaceThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.UpdateWorkspaceThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.WorkspaceThreadQueryRequestVO;
import com.hearyou.anything.manager.vo.response.llm.thread.LlmWorkSpaceThreadResponseVO;
import com.hearyou.common.base.Result;

import java.util.List;

public interface LlmWorkSpaceThreadFacade {


    /**
     * 校验工作区下线程名称是否重复
     * @param workSlug
     * @param name
     * @return
     */
    Result<Boolean> checkWorkThreadName(String workSlug,String name);

    /**
     * 添加工作区线程
     * @param requestVO 添加请求参数
     * @return 添加结果
     */
    Result<String> addWorkspaceThread(AddWorkspaceThreadRequestVO requestVO);

    /**
     * 更新工作区线程
     * @param requestVO 更新请求参数
     * @return 更新结果
     */
    Result<String> updateWorkspaceThread(UpdateWorkspaceThreadRequestVO requestVO);

    /**
     * 删除工作区线程
     * @param workspaceThreadCode 工作区线程编码
     * @return 删除结果
     */
    Result<String> deleteWorkspaceThread(String workspaceThreadCode);

    /**
     * 根据编码查询工作区线程详情
     * @param workspaceThreadCode 工作区线程编码
     * @return 工作区线程详情
     */
    Result<LlmWorkSpaceThreadResponseVO> getWorkspaceThreadByCode(String workspaceThreadCode);

    /**
     * 查询工作区线程列表
     * @param queryVO 查询条件
     * @return 工作区线程列表
     */
    Result<List<LlmWorkSpaceThreadResponseVO>> getWorkspaceThreadList(WorkspaceThreadQueryRequestVO queryVO);
}
