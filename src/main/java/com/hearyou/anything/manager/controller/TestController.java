package com.hearyou.anything.manager.controller;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hearyou.anything.manager.convert.generate.CodeConvertFactory;
import com.hearyou.anything.manager.entity.SysUserRoleEntity;
import com.hearyou.anything.manager.entity.UserInfoEntity;
import com.hearyou.anything.manager.enums.LlmRoleEnum;
import com.hearyou.anything.manager.llm.LlmWorkspaceApi;
import com.hearyou.anything.manager.service.SysRolePermissionService;
import com.hearyou.anything.manager.service.SysRoleService;
import com.hearyou.anything.manager.service.SysUserRoleService;
import com.hearyou.anything.manager.service.UserInfoService;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.conf.expand.RestClientService;
import com.hearyou.anything.manager.llm.LlmAdminApi;
import com.hearyou.anything.manager.llm.LlmAuthorizationApi;
import com.hearyou.anything.manager.llm.LlmThreadApi;
import com.hearyou.anything.manager.vo.request.llm.thread.ThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.UserQueryRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.AddWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.UpdateWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmUsersResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatHistoryResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatTextResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ThreadResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.ThreadItemResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspaceResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResponseVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.core5.net.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试 控制器
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/27 18:54
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    private RestClientService restClientservice;

    private LlmAuthorizationApi llmAuthorizationApi;

    private LlmAdminApi llmAdminApi;

    private LlmWorkspaceApi llmWorkspaceApi;

    @Autowired
    private LlmThreadApi llmThreadApi;

    private SysUserRoleService userRoleService;

    private UserInfoService userInfoService;

    private SysRoleService roleService;

    private SysRolePermissionService rolePermissionService;


    @Autowired
    public TestController(RestClientService restClientservice, LlmAuthorizationApi llmAuthorizationApi,
                          LlmAdminApi llmAdminApi, LlmWorkspaceApi llmWorkspaceApi,
                          LlmThreadApi llmThreadApi, SysUserRoleService userRoleService,
                          UserInfoService userInfoService, SysRoleService roleService,
                          SysRolePermissionService rolePermissionService) {
        this.restClientservice = restClientservice;
        this.llmAuthorizationApi = llmAuthorizationApi;
        this.llmAdminApi = llmAdminApi;
        this.llmWorkspaceApi = llmWorkspaceApi;
        this.llmThreadApi = llmThreadApi;
        this.userRoleService = userRoleService;
        this.userInfoService = userInfoService;
        this.roleService = roleService;
        this.rolePermissionService = rolePermissionService;
    }


    @SneakyThrows
    @GetMapping("/test1")
    public Result<SaSession> testLoginAndGetUrl(){
        StpUtil.login("admin","PC");
        String url = "https://www.baidu.com";
        //调用请求，测试拦截器
        URI uri = new URIBuilder(url).addParameter("name", "hearyou").build();
        Map<String,Object> params = new LinkedHashMap<>();
        params.put("name","hearyou");
        String responseBody = restClientservice.get(url, params, String.class);
        /*String body = restClient.get().uri(uri).retrieve().body(String.class);
        log.info("body:{}",body);*/
        log.info("responseBody:{}",responseBody);
        return Result.success(StpUtil.getTokenSession());
    }

    @GetMapping("/llm_is_multi_user_mode")
    public Result<Boolean> testMultiUserMode(){
        Result<Boolean> result = llmAdminApi.isMultiUserMode();
        return result;
    }

    @GetMapping("/llm_get_users")
    public Result<LlmUsersResponseVO> testLlmGetUsers(){
        Result<LlmUsersResponseVO> usersResult = llmAdminApi.getUsers();
        return usersResult;
    }

    @GetMapping("/llm_get_workspaces")
    public Result<WorkspacesResponseVO> testLlmGetWorkspaces(){
        Result<WorkspacesResponseVO> workspacesResult = llmWorkspaceApi.getWorkspaces();
        return workspacesResult;
    }

    @PostMapping("/word/add")
    public Result<WorkspacesResultResponseVO> addWorkspace(@RequestBody AddWorkspaceRequestVO requestVO){
        return llmWorkspaceApi.addWorkspace(requestVO);
    }

    @DeleteMapping("/word/delete/{slug}")
    public Result<String> deleteWorkspace(@PathVariable("slug") String slug){

        Result<WorkspaceResponseVO> workspaceBySlugResult = llmWorkspaceApi.getWorkspaceBySlug(slug);
        if (workspaceBySlugResult.isFailure()){
            return Result.failure("删除工作区失败!"+workspaceBySlugResult.getMessage());
        }

        WorkspaceResponseVO workspaceResponse = workspaceBySlugResult.getData();

        List<ThreadItemResponseVO> threads = workspaceResponse.getThreads();
        if(CollectionUtil.isNotEmpty(threads)){
            return Result.failure("删除工作区失败!工作区下存在对话线程,请先删除对话线程!");
        }

        return llmWorkspaceApi.deleteWorkspace(slug);
    }

    @PostMapping("/word/{slug}/update")
    Result<WorkspacesResultResponseVO> updateWorkspace(@RequestBody UpdateWorkspaceRequestVO requestVO,@PathVariable("slug") String slug){
        return  llmWorkspaceApi.updateWorkspace(requestVO,slug);
    };

    @PostMapping("thread/{slug}/add")
    Result<ThreadResultResponseVO> addThread(@RequestBody ThreadRequestVO requestVO, @PathVariable("slug") String workSpaceSlug){
        return llmThreadApi.addThread(requestVO,workSpaceSlug);
    };


    @GetMapping("chat/history")
    public Result<ChatHistoryResponseVO> findChatHistory(@RequestParam("work_space_slug") String workSpaceSlug,@RequestParam("thread_slug") String threadSlug) {

        return llmThreadApi.findChatHistory(workSpaceSlug,threadSlug);
    }


    /**
     * 在线程中发起对话
     * @param workSpaceSlug
     * @param threadSlug
     * @param requestVO
     * @return
     */
    @GetMapping("thread/chat")
    public Result<ChatTextResponseVO> chatThread(@RequestParam("work_space_slug") String workSpaceSlug,@RequestParam("thread_slug") String threadSlug, @RequestBody UserQueryRequestVO requestVO){
        return llmThreadApi.chatThread(workSpaceSlug,threadSlug,requestVO);
    }


    @GetMapping("/init_user_role")
    public Result<String> testInitUserAndRole(){
        //先查询用户是否存在
        LambdaQueryWrapper<UserInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfoEntity::getUserName,"hearyou");
        UserInfoEntity userInfoEntity = userInfoService.getOne(queryWrapper);
        if(userInfoEntity == null){
            UserInfoEntity saveUserInfoEntity = new UserInfoEntity();
            saveUserInfoEntity.setUserName("hearyou");
            saveUserInfoEntity.setPassword("hearyou2025");
            saveUserInfoEntity.setNickName("知识库超级管理员");
            saveUserInfoEntity.setName("超级管理员");
            userInfoService.save(saveUserInfoEntity);

            //初始化超管和角色绑定关系
            SysUserRoleEntity userRoleEntity = new SysUserRoleEntity();
            userRoleEntity.setRoleCode(CodeConvertFactory.generateDateCodeByRedis("sysUserRoleRedisCodeGenerate","UR",6));
            userRoleEntity.setRoleCode(LlmRoleEnum.ADMINISTRATOR.getRoleCode());
            userRoleEntity.setUserName(userInfoEntity.getUserName());
            userRoleService.save(userRoleEntity);
        }
        return Result.success("初始化成功!");
    }
}
