package com.hearyou.anything.manager.controller;


import cn.hutool.core.util.StrUtil;
import com.hearyou.anything.manager.cons.LlmConst;
import com.hearyou.anything.manager.enums.FileTypeEnum;
import com.hearyou.anything.manager.exception.CommonException;
import com.hearyou.anything.manager.vo.request.llm.document.LlmWorkFileRequestVO;
import com.hearyou.anything.manager.vo.request.llm.document.UpdateWorkDocumentRequestVO;
import com.hearyou.anything.manager.vo.response.document.*;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspaceResponseVO;

import java.util.ArrayList;
import java.util.Arrays;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.llm.DocumentApi;
import com.hearyou.common.minio.dto.response.UpLoadResponseDTO;
import com.hearyou.common.minio.service.MinioOperationalService;
import io.minio.errors.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;


/**
 * Llm 文档 控制器
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/14 18:34
 */
@Slf4j
@RequestMapping("/llm_document")
@RestController
public class DocumentController {


    @Autowired
    private DocumentApi documentApi;


    @Autowired
    private MinioOperationalService minioOperationalService;

    /**
     * 列出本地存储的文件
     * @return
     */
    @GetMapping("/find/local/files")
    public Result<LocalFilesResponseVO> findLocalFiles(){
        return documentApi.findLocalFiles();
    }


    /**
     * 根据name查找文件
     * @param name
     * @return
     */
    @GetMapping("/find/file/by/name")
    public Result<FileSystemItemResponseVO> findFileByName(@RequestParam("name") String name){
        return documentApi.findFileByName(name);
    }


    /**
     * 上传文件
     * @param file        文件
     * @param parentCode  上级文件编码
     * @return 上传结果
     */
    @PostMapping("/upload")
    public Result<DocumentResultResponseVO> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "parent_code", required = false,defaultValue = "0")String parentCode,
            @RequestParam(value = "workspace_slug") String workspaceSlug) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {


        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.contains(".")) {
            return Result.failure("文件名无效");
        }
        
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!FileTypeEnum.isSupported(fileExtension)) {
            return Result.failure("不支持的文件类型，仅支持: .pdf, .docx, .txt, .md");
        }
        
        // 验证文件大小
        long maxFileSize = 50 * 1024 * 1024; // 50MB
        if (file.getSize() > maxFileSize) {
            log.error("文件上传失败: 文件过大 - {} bytes, 最大允许: {} bytes", file.getSize(), maxFileSize);
            return Result.failure("文件过大，最大允许50MB");
        }

        // 调用DocumentService进行文件上传
        Result<DocumentResultResponseVO> result = documentApi.uploadFile(file, LlmConst.DEFAULT_FOLDER_NAME);

        if (result.isFailure()){
            return Result.failure("llm文件上传失败"+result.getMessage());
        }

        DocumentResponseVO document = result.getData().getDocuments().get(0);


        //上传到文件服务器
        UpLoadResponseDTO upLoadResponseDTO = minioOperationalService.uploadFile(LlmConst.DEFAULT_FOLDER_NAME, file);


        //保存数据库
        LlmWorkFileRequestVO llmWorkFileRequestVO = new LlmWorkFileRequestVO();
        llmWorkFileRequestVO.setFileName(file.getOriginalFilename());
        llmWorkFileRequestVO.setFileSize((int) file.getSize());
        llmWorkFileRequestVO.setLlmFileName(document.getLocation());
        llmWorkFileRequestVO.setFileMd5Sign(upLoadResponseDTO.getFileAllPath());
        llmWorkFileRequestVO.setParentCode(parentCode);
        
        // 设置文件后缀
        if (originalFilename != null && originalFilename.contains(".")) {
            String fileSuffix = originalFilename.substring(originalFilename.lastIndexOf("."));
            llmWorkFileRequestVO.setFileSuffix(fileSuffix);
        }
        
        // 设置工作区slug
        llmWorkFileRequestVO.setWorkspaceSlug(workspaceSlug);
        documentApi.addLlmWorkFile(llmWorkFileRequestVO);


        return result;
    }


    /**
     * 创建文件夹
     * @param parentCode 上级文件编码
     * @param fileName   文件夹名称
     * @return
     */
    @PostMapping("create_folder")
    Result<String> addFolder( @RequestParam(value = "parent_code", required = false,defaultValue = "0")String parentCode,
                                        @RequestParam(value = "file_name")String fileName,
                                        @RequestParam(value = "workspace_slug") String workspaceSlug){

        LlmWorkFileRequestVO llmWorkFileRequestVO = new LlmWorkFileRequestVO();
        llmWorkFileRequestVO.setParentCode(parentCode);
        llmWorkFileRequestVO.setFileName(fileName);
        
        // 设置文件夹后缀为 /
        llmWorkFileRequestVO.setFileSuffix("/");
        
        // 设置工作区slug
        llmWorkFileRequestVO.setWorkspaceSlug(workspaceSlug);

        return documentApi.addFolder(llmWorkFileRequestVO);

    }



    /**
     * 删除文件
     * @param fileCode
     * @return
     */
    @DeleteMapping("/delete_folder/{fileCode}")
    Result<String> deleteFolder(@PathVariable("fileCode")String fileCode){
        return documentApi.deleteFile(fileCode);
    }


    /**
     * 获取文件列表
     * @param parentCode    父编码
     * @param fileName      文件名称
     * @param whetherEnable 是否启用
     * @param fileSuffix    文件后缀
     * @return
     */
    @GetMapping("file_list")
    public Result<List<LlmWorkFileResponseVO>> findFileList(@RequestParam(value = "parent_code",required = false,defaultValue = "0") String parentCode,
                                                            @RequestParam(value = "file_name",required = false) String fileName,
                                                            @RequestParam(value = "whether_enable",required = false)Boolean whetherEnable,
                                                            @RequestParam(value = "file_suffix",required = false)String fileSuffix,
                                                            @RequestParam(value = "workspace_slug") String workspaceSlug){
        return documentApi.findFileList(parentCode, fileName, whetherEnable, fileSuffix, workspaceSlug);
    }



    /**
     * 下载文件
     * @param fileCode
     * @return
     * @throws ServerException
     * @throws InsufficientDataException
     * @throws ErrorResponseException
     * @throws IOException
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws InvalidResponseException
     * @throws XmlParserException
     * @throws InternalException
     */
    @GetMapping("/download/{file_code}")
    public ResponseEntity<byte[]> downloadFile(@PathVariable("file_code") String fileCode) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        if (StrUtil.isBlank(fileCode)) {
            throw new CommonException("文件编码为空");
        }

        Result<LlmWorkFileResponseVO> result = documentApi.findFileByCode(fileCode);
        if (result.isFailure()) {
            throw new CommonException(result.getMessage());
        }

        LlmWorkFileResponseVO fileInfo = result.getData();

        String fileMd5Sign = fileInfo.getFileMd5Sign();


        String fileName = fileMd5Sign.substring(fileMd5Sign.lastIndexOf("/") + 1);
        String filePath = fileMd5Sign.substring(0, fileMd5Sign.lastIndexOf("/"));


        ResponseEntity<byte[]> responseEntity = minioOperationalService.downloadFile(filePath,fileName);

        return responseEntity;
    }

    /**
     * 文件启用停用
     * @param fileCode
     * @param whetherEnable
     * @return
     */
    @PutMapping("/enable/{fileCode}")
    public Result<String> changeFileStatus(@PathVariable("fileCode") String fileCode,
                                           @RequestParam("whether_enable") Boolean whetherEnable){
        return documentApi.changeFileStatus(fileCode, whetherEnable);
    }



    /**
     * 获取支持的文件类型列表
     * @return
     */
    @GetMapping("/file_types")
    public Result<List<FileTypeResponseVO>> getFileTypes(){
        List<FileTypeResponseVO> fileTypes = new ArrayList<>();
        for (FileTypeEnum fileType : FileTypeEnum.values()) {
            fileTypes.add(new FileTypeResponseVO(fileType.getExtension(), fileType.getDescription()));
        }
        // 单独添加文件夹类型
        fileTypes.add(new FileTypeResponseVO("/", "文件夹"));
        return Result.success(fileTypes);
    }

    /**
     * 修改文件名
     * @param fileCode
     * @param fileName
     * @return
     */
    @PutMapping("/update/{fileCode}")
    public Result<String> updateFile(@PathVariable("fileCode") String fileCode,
                                     @RequestParam("file_name") String fileName){
        return documentApi.updateFile(fileCode, fileName);
    }




}
