package com.hearyou.anything.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.hearyou.anything.manager.vo.response.business.LlmUserInfoResponseVO;
import com.hearyou.anything.manager.vo.response.business.LlmUserLoginResponseVO;
import com.hearyou.common.base.PageResult;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.enums.LlmRoleEnum;
import com.hearyou.anything.manager.uaa.LlmUseruaaFacade;
import com.hearyou.anything.manager.vo.request.business.LoginRequestVO;
import com.hearyou.anything.manager.vo.request.business.UserInfoRequestVO;
import com.hearyou.anything.manager.vo.response.business.InputResponseVO;
import com.hearyou.common.utils.data.PageUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Llm 用户管理 控制器
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/10 18:34
 */
@RequestMapping("/llm_user")
@RestController
public class LlmUserController {

   private LlmUseruaaFacade llmUseruaaFacade;

   @Value("${anything-llm.admin-username:admin}")
   private String adminUsername;

   @Value("${anything-llm.admin-password:hearyou2025}")
   private String adminPassword;

   @Autowired
   public LlmUserController(LlmUseruaaFacade llmUseruaaFacade) {
      this.llmUseruaaFacade = llmUseruaaFacade;
   }

   /**
    * 获取 角色下拉框
    * @return
    */
   @GetMapping("/find/role_input_list")
   public Result<List<InputResponseVO>> findRoleInputList(){
      List<InputResponseVO> inputResponseVOList = new ArrayList<>();
      LlmRoleEnum[] values = LlmRoleEnum.values();
      for (LlmRoleEnum roleEnum : values) {
         InputResponseVO inputResponseVO = new InputResponseVO();
         inputResponseVO.setInputCode(roleEnum.getRoleCode());
         inputResponseVO.setInputName(roleEnum.getRoleName());
         inputResponseVOList.add(inputResponseVO);
      }
      return Result.success(inputResponseVOList);
   }


   /**
    * 根据条件 分页 查询用户列表
    * @param likeUserName 用户名模糊查询|admin
    * @param roleCodes 角色列表|admin,manager
    * @param workSlugs 工作区列表|dwajdwaddwad1,dwaddwerwew2
    * @param openStatus 启用状态 -1:全部 0:启用 1:禁用|-1
    * @param page 当前页|1
    * @param pageSize 每页条数|10
    * @return
    */
   @GetMapping("/find_user_list")
   public Result<List<LlmUserInfoResponseVO>> findUserList(HttpServletResponse response,
                                                                 @RequestParam(value = "like_user_name",required = false)String likeUserName,
                                                                 @RequestParam(value = "role_codes",required = false)String roleCodes,
                                                                 @RequestParam(value = "work_slugs",required = false)String workSlugs,
                                                                 @RequestParam(value = "open_status",required = false)Integer openStatus,
                                                                 @RequestParam(value = "page",required = true,defaultValue = "1")Integer page,
                                                                 @RequestParam(value = "page_size",required = true,defaultValue = "10")Integer pageSize){

      if(page < 0){
         page = 1;
      }
      if(pageSize < 0){
         pageSize = 10;
      }
      if(null != openStatus && (openStatus < -1 || openStatus > 1)){
         return Result.failure("启用/禁用状态参数异常!");
      }


      final List<String> roleCodeList = Arrays.stream(Optional.ofNullable(roleCodes).orElse("").split(","))
              .map(String::trim)
              .filter(s -> !StringUtils.isBlank(s))
              .collect(Collectors.toList());

      final List<String> workSlugList = Arrays.stream(Optional.ofNullable(workSlugs).orElse("").split(","))
              .map(String::trim)
              .filter(s -> !StringUtils.isBlank(s))
              .collect(Collectors.toList());

      //进行查询分页
      Result<PageResult<LlmUserInfoResponseVO>> pageResult = llmUseruaaFacade.findUserList(likeUserName, roleCodeList, workSlugList, openStatus, page, pageSize);
      if(pageResult.isFailure()){
         return Result.failure(pageResult.getMessage());
      }

      PageResult<LlmUserInfoResponseVO> pageResultData = pageResult.getData();
      PageUtil.setPageResponseHeader(response, pageResultData.getTotal());
      return Result.success(pageResultData.getRecodes());
   }

   /**
    * 创建用户并绑定角色和工作区权限
    * @param requestVO
    * @return
    */
   @PostMapping("/save_user_bind")
   public Result<Boolean> saveUserBind(@Validated @RequestBody UserInfoRequestVO requestVO){
      if(null == requestVO){
         return Result.failure("请求参数不能为空!");
      }
      return llmUseruaaFacade.saveUserBind(requestVO);
   }

   /**
    * 修改用户信息 以及 绑定角色和工作区权限
    * @param requestVO
    * @return
    */
   @PutMapping("/update_user_bind")
   public Result<Boolean> updateUserBind(@Validated @RequestBody UserInfoRequestVO requestVO){
      if(null == requestVO){
         return Result.failure("请求参数不能为空!");
      }
      return llmUseruaaFacade.updateUserBind(requestVO);
   }


   /**
    * 更新用户状态
    * @param username 用户名|admin
    * @param disabledStatus 禁用状态|false
    * @return
    */
   @PostMapping("/update_user_status/{username}")
   public Result<Boolean> updateUserStatus(@PathVariable("username")String username, Boolean disabledStatus){
      if(StrUtil.isBlank(username)){
         return Result.failure("修改用户状态失败!用户名不能为空!");
      }
      return llmUseruaaFacade.updateUserStatus(username, disabledStatus);
   }

   /**
    * 用户名-密码登录
    * @param requestVO
    * @return
    */
   @PostMapping("/login")
   public Result<LlmUserLoginResponseVO> login(@Validated @RequestBody LoginRequestVO requestVO){
      Result<LlmUserLoginResponseVO> loginResult = llmUseruaaFacade.login(requestVO);
      if(loginResult.isFailure()){
         return Result.failure("登录失败!" + loginResult.getMessage());
      }
      return loginResult;
   }
}
