package com.hearyou.anything.manager.controller;


import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.hearyou.anything.manager.facade.LlmWorkSpaceFacade;
import com.hearyou.anything.manager.facade.LlmWorkSpaceThreadFacade;
import com.hearyou.anything.manager.llm.LlmWorkspaceApi;
import com.hearyou.anything.manager.vo.request.llm.thread.*;
import com.hearyou.anything.manager.vo.request.llm.work.WorkspaceQueryRequestVO;
import com.hearyou.anything.manager.vo.response.llm.thread.LlmWorkSpaceThreadResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.*;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.enums.LlmRoleEnum;
import com.hearyou.anything.manager.llm.LlmAdminApi;
import com.hearyou.anything.manager.llm.LlmThreadApi;
import com.hearyou.anything.manager.uaa.LlmUseruaaFacade;
import com.hearyou.anything.manager.vo.request.llm.work.AddWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.UpdateWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatHistoryResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatTextResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ThreadResultResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Llm 工作区 控制器
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/11 14:58
 */
@RequestMapping("/llm_workspace")
@RestController
public class WorkSpaceController {


    @Autowired
    private LlmWorkspaceApi llmWorkspaceApi;


    @Autowired
    private LlmThreadApi llmThreadApi;


    @Autowired
    private LlmUseruaaFacade llmUseruaaFacade;

    @Autowired
    private LlmWorkSpaceFacade llmWorkSpaceFacade;

    @Autowired
    private LlmWorkSpaceThreadFacade llmWorkSpaceThreadFacade;


    /**
     * 获取所有的工作区
     * @return
     */
    @GetMapping("/get_workspaces")
    public Result<WorkspacesResponseVO> getLlmGetWorkspaces(){
        Result<WorkspacesResponseVO> workspacesResult = llmWorkspaceApi.getWorkspaces();
        return workspacesResult;
    }


    /**
     * 根据权限来获取对应的工作区（返回LLM API实体）
     * @return
     */
    @GetMapping("/get_workspaces/auth")
    public Result<WorkspacesResponseVO> getLlmGetWorkspacesByAuth(){

        Result<WorkspacesResponseVO> workspacesResult = llmWorkspaceApi.getWorkspaces();

        //判断是否是超级管理员
        if (StpUtil.hasRole(LlmRoleEnum.ADMINISTRATOR.getRoleCode())){
            return workspacesResult;
        }else{
            //获取当前用户的工作区
            Result<List<String>> workSlugsResult = llmUseruaaFacade.findWorkSlugs(StpUtil.getLoginIdAsString());
            if (workSlugsResult.isFailure()){
                return Result.failure(workSlugsResult.getMessage());
            }
            List<String> workSlugs = workSlugsResult.getData();

            if (CollectionUtil.isNotEmpty(workSlugs)){

                WorkspacesResponseVO workspaces = workspacesResult.getData();
                List<WorkspaceResponseVO> workspacesList = workspaces.getWorkspaces();
                workspacesList = workspacesList.stream().filter(workspace -> workSlugs.contains(workspace.getSlug())).collect(Collectors.toList());
                workspaces.setWorkspaces(workspacesList);
            }else{
                workspacesResult.getData().setWorkspaces(new ArrayList<>());
            }
        }


        return workspacesResult;
    }


    /**
     * 根据权限来获取对应的工作区（返回数据库实体）
     * @return
     */
    @GetMapping("/get_workspaces/auth/db")
    public Result<List<LlmWorkSpaceResponseVO>> getLlmGetWorkspacesByAuthDB(){


        List<LlmWorkSpaceResponseVO> workList = new ArrayList<>();

        WorkspaceQueryRequestVO queryVO = new WorkspaceQueryRequestVO();
        //判断是否是超级管理员
        if (!StpUtil.hasRole(LlmRoleEnum.ADMINISTRATOR.getRoleCode())){
            //获取当前用户的工作区
            Result<List<String>> workSlugsResult = llmUseruaaFacade.findWorkSlugs(StpUtil.getLoginIdAsString());
            if (workSlugsResult.isFailure()){
                return Result.failure(workSlugsResult.getMessage());
            }
            List<String> workSlugs = workSlugsResult.getData();
            if (CollectionUtil.isNotEmpty(workSlugs)){
                queryVO.setWorkspaceSlugs(workSlugs);

            }else{
                return Result.success(workList);
            }

        }

        Result<List<LlmWorkSpaceResponseVO>> workspacesResultResult = llmWorkSpaceFacade.getWorkspaceList(queryVO);

        workList = workspacesResultResult.getData();

        if (CollectionUtil.isNotEmpty(workList)){
            List<String> workSlugs = workList.stream().map(workspace -> workspace.getWorkspaceSlug()).collect(Collectors.toList());
            WorkspaceThreadQueryRequestVO workspaceThreadQueryRequestVO = new WorkspaceThreadQueryRequestVO();
            workspaceThreadQueryRequestVO.setWorkspaceSlugs(workSlugs);
            Result<List<LlmWorkSpaceThreadResponseVO>> workspaceThreadListResult = llmWorkSpaceThreadFacade.getWorkspaceThreadList(workspaceThreadQueryRequestVO);
            if (workspaceThreadListResult.isFailure()){
                return Result.failure(workspaceThreadListResult.getMessage());
            }

            List<LlmWorkSpaceThreadResponseVO> threadList = workspaceThreadListResult.getData();

            if (CollectionUtil.isNotEmpty(threadList)){
                //把线程添加到工作区下
                Map<String, List<LlmWorkSpaceThreadResponseVO>> threadMap = threadList.stream().collect(Collectors.groupingBy(LlmWorkSpaceThreadResponseVO::getWorkspaceSlug));

                for (LlmWorkSpaceResponseVO llmWorkSpaceResponseVO : workList) {
                    llmWorkSpaceResponseVO.setThreadList(threadMap.get(llmWorkSpaceResponseVO.getWorkspaceSlug()));
                }
            }

        }


        return Result.success(workList);
    }




    /**
     * 新增工作区
     * @param requestVO
     * @return
     */
    @PostMapping("/word/add")
    public Result<WorkspacesResultResponseVO> addWorkspace(@RequestBody AddWorkspaceRequestVO requestVO){

        String name = requestVO.getName();
        Result<Boolean> checkWorkspaceNameResult = llmWorkSpaceFacade.checkWorkspaceName(name);
        if (checkWorkspaceNameResult.isFailure()){
            return Result.failure(checkWorkspaceNameResult.getMessage());
        }
        if (!checkWorkspaceNameResult.getData()){
            return Result.failure("工作区名称已存在");
        }

        Result<WorkspacesResultResponseVO> workspacesResultResponseVOResult = llmWorkspaceApi.addWorkspace(requestVO);
        if (workspacesResultResponseVOResult.isFailure()){
            return Result.failure(workspacesResultResponseVOResult.getMessage());
        }

        WorkspaceResponseVO workspace = workspacesResultResponseVOResult.getData().getWorkspace();
        requestVO.setWorkspaceSlug(workspace.getSlug());

        Result<String> addWorkspaceResult = llmWorkSpaceFacade.addWorkspace(requestVO);

        if (addWorkspaceResult.isFailure()){
            return Result.failure(addWorkspaceResult.getMessage());
        }

        return workspacesResultResponseVOResult;
    }


    /**
     * 删除工作区
     * @param slug 工作区slug|ddw51dw5d4wad5
     * @return
     */
    @DeleteMapping("/word/delete/{slug}")
    public Result<String> deleteWorkspace(@PathVariable("slug") String slug){


        //校验工作区下是否有线程
        Result<Boolean> checkWorkThreadDeleteResult = llmWorkSpaceFacade.checkWorkThreadDelete(slug);

        if (checkWorkThreadDeleteResult.isFailure()){
            return Result.failure(checkWorkThreadDeleteResult.getMessage());
        }

        if (!checkWorkThreadDeleteResult.getData()){
            return Result.failure("删除工作区失败!工作区下存在对话线程,请先删除对话线程!");
        }

        Result<WorkspaceResponseVO> workspaceBySlugResult = llmWorkspaceApi.getWorkspaceBySlug(slug);
        if (workspaceBySlugResult.isFailure()){
            return Result.failure("删除工作区失败!"+workspaceBySlugResult.getMessage());
        }

        WorkspaceResponseVO workspaceResponse = workspaceBySlugResult.getData();

        List<ThreadItemResponseVO> threads = workspaceResponse.getThreads();
        if(CollectionUtil.isNotEmpty(threads)){
            return Result.failure("删除工作区失败!工作区下存在对话线程,请先删除对话线程!");
        }

        //api删除线程
        Result<String> deleteWorkspaceApiResult = llmWorkspaceApi.deleteWorkspace(slug);

        if (deleteWorkspaceApiResult.isFailure()){
            return Result.failure("删除工作区失败!"+deleteWorkspaceApiResult.getMessage());
        }

        //数据库删除线程
        Result<String> deleteWorkspaceResult = llmWorkSpaceFacade.deleteWorkspace(slug);

        if (deleteWorkspaceResult.isFailure()){
            return Result.failure("删除工作区失败!"+deleteWorkspaceResult.getMessage());
        }


        return Result.success("删除成功");
    }


    /**
     * 更新工作区
     * @param requestVO
     * @return
     */
    @PostMapping("/work/update")
    Result<String> updateWorkspace(@RequestBody UpdateWorkspaceRequestVO requestVO){

        Result<WorkspacesResultResponseVO> workspacesResultResponseResult = llmWorkspaceApi.updateWorkspace(requestVO, requestVO.getWorkspaceCode());


        if (workspacesResultResponseResult.isFailure()){
            return Result.failure(workspacesResultResponseResult.getMessage());
        }

        Result<String> updateWorkspaceResult = llmWorkSpaceFacade.updateWorkspace(requestVO);
        if (updateWorkspaceResult.isFailure()){
            return Result.failure(updateWorkspaceResult.getMessage());
        }


        return Result.success("更新成功");
    };


    /**
     * 新增线程
     * @param requestVO
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @return
     */
    @PostMapping("thread/{slug}/add")
    Result<String> addThread(@RequestBody ThreadRequestVO requestVO, @PathVariable("slug") String workSpaceSlug){
        Result<Boolean> checkWorkThreadNameResult = llmWorkSpaceThreadFacade.checkWorkThreadName(workSpaceSlug, requestVO.getName());
        if (checkWorkThreadNameResult.isFailure()){
            return Result.failure(checkWorkThreadNameResult.getMessage());
        }

        if (!checkWorkThreadNameResult.getData()){
            return Result.failure("添加失败,线程名称已存在");
        }

        Result<ThreadResultResponseVO> threadResultResponseVOResult = llmThreadApi.addThread(requestVO, workSpaceSlug);
        if (threadResultResponseVOResult.isFailure()){
            return Result.failure(threadResultResponseVOResult.getMessage());
        }

        AddWorkspaceThreadRequestVO addWorkspaceThreadRequestVO = new AddWorkspaceThreadRequestVO();
        addWorkspaceThreadRequestVO.setWorkspaceThreadName(requestVO.getName());
        addWorkspaceThreadRequestVO.setWorkspaceSlug(workSpaceSlug);
        addWorkspaceThreadRequestVO.setWorkspaceThreadSlug(threadResultResponseVOResult.getData().getThread().getSlug());
        addWorkspaceThreadRequestVO.setUserName(StpUtil.getLoginIdAsString());

        Result<String> addWorkspaceThreadResult = llmWorkSpaceThreadFacade.addWorkspaceThread(addWorkspaceThreadRequestVO);
        if (addWorkspaceThreadResult.isFailure()){
            return Result.failure(addWorkspaceThreadResult.getMessage());
        }

        return Result.success("添加成功");
    };


    /**
     * 修改线程名称
     * @param requestVO
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @return
     */
    @PostMapping("thread/{slug}/update")
    Result<String> updateThread(@RequestBody ThreadRequestVO requestVO, @PathVariable("slug") String workSpaceSlug){

        UpdateWorkspaceThreadRequestVO updateWorkspaceThreadRequestVO = new UpdateWorkspaceThreadRequestVO();
        updateWorkspaceThreadRequestVO.setWorkspaceThreadName(requestVO.getName());
        updateWorkspaceThreadRequestVO.setWorkspaceThreadSlug(requestVO.getSlug());
        updateWorkspaceThreadRequestVO.setWorkspaceSlug(workSpaceSlug);
        updateWorkspaceThreadRequestVO.setUserName(StpUtil.getLoginIdAsString());


        Result<String> updateWorkspaceThreadResult = llmWorkSpaceThreadFacade.updateWorkspaceThread(updateWorkspaceThreadRequestVO);
        if (updateWorkspaceThreadResult.isFailure()){
            return Result.failure(updateWorkspaceThreadResult.getMessage());
        }

        Result<ThreadResultResponseVO> threadResultResponseVOResult = llmThreadApi.updateThread(requestVO, workSpaceSlug);
        if (threadResultResponseVOResult.isFailure()){
            return Result.failure(threadResultResponseVOResult.getMessage());
        }

        return Result.success("更新成功");
    };



    /**
     * 删除线程
     * @param workSpaceSlug
     * @param threadSlug 线程slug|ddw51dw5d4wad5
     * @return
     */
    @DeleteMapping("thread/{workSpaceSlug}/delete/{threadSlug}")
    Result<String> deleteThread(@PathVariable("workSpaceSlug") String workSpaceSlug, @PathVariable("threadSlug") String threadSlug){

        Result<String> deleteThreadResult = llmThreadApi.deleteThread(workSpaceSlug, threadSlug);

        if (deleteThreadResult.isFailure()){
            return Result.failure(deleteThreadResult.getMessage());
        }

        Result<String> deleteWorkspaceThreadResult = llmWorkSpaceThreadFacade.deleteWorkspaceThread(threadSlug);
        if (deleteWorkspaceThreadResult.isFailure()){
            return Result.failure(deleteWorkspaceThreadResult.getMessage());
        }

        return Result.success("删除成功");
    };


    /**
     * 获取线程聊天记录
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @param threadSlug 线程slug|ddw51dw5d4wad5
     * @return
     */
    @GetMapping("chat/history")
    public Result<ChatHistoryResponseVO> findChatHistory(@RequestParam("work_space_slug") String workSpaceSlug, @RequestParam("thread_slug") String threadSlug) {

        return llmThreadApi.findChatHistory(workSpaceSlug,threadSlug);
    }


    /**
     * 在线程中发起对话
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @param threadSlug 线程slug|ddw51dw5d4wad5
     * @param requestVO
     * @return
     */
    @PostMapping("thread/chat")
    public Result<ChatTextResponseVO> chatThread(@RequestParam("work_space_slug") String workSpaceSlug, @RequestParam("thread_slug") String threadSlug, @RequestBody UserQueryRequestVO requestVO){
        return llmThreadApi.chatThread(workSpaceSlug,threadSlug,requestVO);
    }

    /**
     * 在线程中发起流式对话
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @param threadSlug 线程slug|ddw51dw5d4wad5
     * @param requestVO
     * @return SSE流
     */
    @PostMapping(value = "thread/chat/stream", produces = "text/event-stream")
    public SseEmitter chatThreadStream(@RequestParam("work_space_slug") String workSpaceSlug,
                                       @RequestParam("thread_slug") String threadSlug,
                                       @RequestBody UserQueryRequestVO requestVO){
        return llmThreadApi.chatThreadStream(workSpaceSlug, threadSlug, requestVO);
    }

}
