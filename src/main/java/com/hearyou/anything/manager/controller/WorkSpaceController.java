package com.hearyou.anything.manager.controller;


import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.hearyou.anything.manager.llm.LlmWorkspaceApi;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.enums.LlmRoleEnum;
import com.hearyou.anything.manager.llm.LlmAdminApi;
import com.hearyou.anything.manager.llm.LlmThreadApi;
import com.hearyou.anything.manager.uaa.LlmUseruaaFacade;
import com.hearyou.anything.manager.vo.request.llm.thread.ThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.UserQueryRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.AddWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.UpdateWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatHistoryResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatTextResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ThreadResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.ThreadItemResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspaceResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResultResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Llm 工作区 控制器
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/11 14:58
 */
@RequestMapping("/llm_workspace")
@RestController
public class WorkSpaceController {


    @Autowired
    private LlmAdminApi llmAdminApi;


    @Autowired
    private LlmWorkspaceApi llmWorkspaceApi;


    @Autowired
    private LlmThreadApi llmThreadApi;


    @Autowired
    private LlmUseruaaFacade llmUseruaaFacade;


    /**
     * 获取所有的工作区
     * @return
     */
    @GetMapping("/get_workspaces")
    public Result<WorkspacesResponseVO> getLlmGetWorkspaces(){
        Result<WorkspacesResponseVO> workspacesResult = llmWorkspaceApi.getWorkspaces();
        return workspacesResult;
    }


    /**
     * 根据权限来获取对应的工作区
     * @return
     */
    @GetMapping("/get_workspaces/auth")
    public Result<WorkspacesResponseVO> getLlmGetWorkspacesByAuth(){

        Result<WorkspacesResponseVO> workspacesResult = llmWorkspaceApi.getWorkspaces();

        //判断是否是超级管理员
        if (StpUtil.hasRole(LlmRoleEnum.ADMINISTRATOR.getRoleCode())){
            return workspacesResult;
        }else{
            //获取当前用户的工作区
            Result<List<String>> workSlugsResult = llmUseruaaFacade.findWorkSlugs(StpUtil.getLoginIdAsString());
            if (workSlugsResult.isFailure()){
                return Result.failure(workSlugsResult.getMessage());
            }
            List<String> workSlugs = workSlugsResult.getData();

            if (CollectionUtil.isNotEmpty(workSlugs)){

                WorkspacesResponseVO workspaces = workspacesResult.getData();
                List<WorkspaceResponseVO> workspacesList = workspaces.getWorkspaces();
                workspacesList = workspacesList.stream().filter(workspace -> workSlugs.contains(workspace.getSlug())).collect(Collectors.toList());
                workspaces.setWorkspaces(workspacesList);
            }else{
                workspacesResult.getData().setWorkspaces(new ArrayList<>());
            }
        }


        return workspacesResult;
    }


    /**
     * 新增工作区
     * @param requestVO
     * @return
     */
    @PostMapping("/word/add")
    public Result<WorkspacesResultResponseVO> addWorkspace(@RequestBody AddWorkspaceRequestVO requestVO){
        return llmWorkspaceApi.addWorkspace(requestVO);
    }


    /**
     * 删除工作区
     * @param slug 工作区slug|ddw51dw5d4wad5
     * @return
     */
    @DeleteMapping("/word/delete/{slug}")
    public Result<String> deleteWorkspace(@PathVariable("slug") String slug){

        Result<WorkspaceResponseVO> workspaceBySlugResult = llmWorkspaceApi.getWorkspaceBySlug(slug);
        if (workspaceBySlugResult.isFailure()){
            return Result.failure("删除工作区失败!"+workspaceBySlugResult.getMessage());
        }

        WorkspaceResponseVO workspaceResponse = workspaceBySlugResult.getData();

        List<ThreadItemResponseVO> threads = workspaceResponse.getThreads();
        if(CollectionUtil.isNotEmpty(threads)){
            return Result.failure("删除工作区失败!工作区下存在对话线程,请先删除对话线程!");
        }

        return llmWorkspaceApi.deleteWorkspace(slug);
    }


    /**
     * 更新工作区
     * @param requestVO
     * @param slug 工作区slug|ddw51dw5d4wad5
     * @return
     */
    @PostMapping("/word/{slug}/update")
    Result<WorkspacesResultResponseVO> updateWorkspace(@RequestBody UpdateWorkspaceRequestVO requestVO, @PathVariable("slug") String slug){
        return  llmWorkspaceApi.updateWorkspace(requestVO,slug);
    };


    /**
     * 新增线程
     * @param requestVO
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @return
     */
    @PostMapping("thread/{slug}/add")
    Result<ThreadResultResponseVO> addThread(@RequestBody ThreadRequestVO requestVO, @PathVariable("slug") String workSpaceSlug){
        return llmThreadApi.addThread(requestVO,workSpaceSlug);
    };


    /**
     * 修改线程名称
     * @param requestVO
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @return
     */
    @PostMapping("thread/{slug}/update")
    Result<ThreadResultResponseVO> updateThread(@RequestBody ThreadRequestVO requestVO, @PathVariable("slug") String workSpaceSlug){
        return llmThreadApi.updateThread(requestVO,workSpaceSlug);
    };



    /**
     * 删除线程
     * @param workSpaceSlug
     * @param threadSlug 线程slug|ddw51dw5d4wad5
     * @return
     */
    @DeleteMapping("thread/{workSpaceSlug}/delete/{threadSlug}")
    Result<String> deleteThread(@PathVariable("workSpaceSlug") String workSpaceSlug, @PathVariable("threadSlug") String threadSlug){
        return llmThreadApi.deleteThread(workSpaceSlug,threadSlug);
    };


    /**
     * 获取线程聊天记录
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @param threadSlug 线程slug|ddw51dw5d4wad5
     * @return
     */
    @GetMapping("chat/history")
    public Result<ChatHistoryResponseVO> findChatHistory(@RequestParam("work_space_slug") String workSpaceSlug, @RequestParam("thread_slug") String threadSlug) {

        return llmThreadApi.findChatHistory(workSpaceSlug,threadSlug);
    }


    /**
     * 在线程中发起对话
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @param threadSlug 线程slug|ddw51dw5d4wad5
     * @param requestVO
     * @return
     */
    @PostMapping("thread/chat")
    public Result<ChatTextResponseVO> chatThread(@RequestParam("work_space_slug") String workSpaceSlug, @RequestParam("thread_slug") String threadSlug, @RequestBody UserQueryRequestVO requestVO){
        return llmThreadApi.chatThread(workSpaceSlug,threadSlug,requestVO);
    }

    /**
     * 在线程中发起流式对话
     * @param workSpaceSlug 工作区slug|ddw51dw5d4wad5
     * @param threadSlug 线程slug|ddw51dw5d4wad5
     * @param requestVO
     * @return SSE流
     */
    @PostMapping(value = "thread/chat/stream", produces = "text/event-stream")
    public SseEmitter chatThreadStream(@RequestParam("work_space_slug") String workSpaceSlug,
                                       @RequestParam("thread_slug") String threadSlug,
                                       @RequestBody UserQueryRequestVO requestVO){
        return llmThreadApi.chatThreadStream(workSpaceSlug, threadSlug, requestVO);
    }

}
