package com.hearyou.anything.manager.llm;

import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.vo.request.llm.thread.ThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.UserQueryRequestVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatHistoryResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatTextResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ThreadResultResponseVO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

public interface LlmThreadApi {


    /**
     * 新增线程
     * @param requestVO
     * @param workSpaceSlug
     * @return
     */
    Result<ThreadResultResponseVO> addThread(ThreadRequestVO requestVO, String workSpaceSlug);


    /**
     * 修改线程名称
     * @param requestVO
     * @param workSpaceSlug
     * @return
     */
    Result<ThreadResultResponseVO> updateThread(ThreadRequestVO requestVO, String workSpaceSlug);


    /**
     * 删除线程
     * @param workSpaceSlug
     * @param threadSlug
     * @return
     */
    Result<String> deleteThread(String workSpaceSlug, String threadSlug);


    /**
     * 查询聊天历史记录
     * @param workSpaceSlug
     * @param threadSlug
     * @return
     */
    Result<ChatHistoryResponseVO> findChatHistory(String workSpaceSlug, String threadSlug);


    /**
     * 在线程中发起对话
     * @param workSpaceSlug
     * @param threadSlug
     * @param requestVO
     * @return
     */
    Result<ChatTextResponseVO> chatThread(String workSpaceSlug, String threadSlug, UserQueryRequestVO requestVO);

    /**
     * 在线程中发起流式对话
     * @param workSpaceSlug
     * @param threadSlug
     * @param requestVO
     * @return
     */
    SseEmitter chatThreadStream(String workSpaceSlug, String threadSlug, UserQueryRequestVO requestVO);

}
