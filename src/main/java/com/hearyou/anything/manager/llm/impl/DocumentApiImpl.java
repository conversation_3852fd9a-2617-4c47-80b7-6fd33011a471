package com.hearyou.anything.manager.llm.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hearyou.anything.manager.convert.DocumentConvert;
import com.hearyou.anything.manager.entity.LlmWorkFileEntity;
import com.hearyou.anything.manager.service.LlmWorkFileService;
import com.hearyou.anything.manager.vo.request.llm.document.LlmWorkFileRequestVO;
import com.hearyou.anything.manager.vo.request.llm.document.UpdateWorkDocumentRequestVO;
import com.hearyou.anything.manager.vo.response.document.*;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspaceResponseVO;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.llm.DocumentApi;
import com.hearyou.anything.manager.llm.LlmCommonApi;
import com.hearyou.anything.manager.vo.request.llm.document.FolderRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Component
public class DocumentApiImpl implements DocumentApi {

    @Autowired
    private LlmCommonApi llmCommonApi;


    @Autowired
    private LlmWorkFileService llmWorkFileService;

    @Autowired
    private DocumentConvert documentConvert;

    /**
     * 列出本地存储的文件
     * @return
     */
    @Override
    public Result<LocalFilesResponseVO> findLocalFiles() {

        LocalFilesResponseVO localFilesResponseVO = llmCommonApi.getResult("/documents", null, LocalFilesResponseVO.class);

        return Result.success(localFilesResponseVO);

    }


    /**
     * 根据name查找文件
     * @param name
     * @return
     */
    @Override
    public Result<FileSystemItemResponseVO> findFileByName(String name) {

        if (StrUtil.isBlank(name)) {
            return Result.failure("name不能为空");
        }


        FileSystemItemResponseVO fileSystemItemResponseVO = llmCommonApi.getResult("/document/"+name, null, FileSystemItemResponseVO.class);

        return Result.success(fileSystemItemResponseVO);
    }

    /**
     * 上传文件到文档系统
     * @param file 要上传的MultipartFile文件
     * @param folderName 文件夹
     * @return 上传结果
     */
    @Override
    public Result<DocumentResultResponseVO> uploadFile(MultipartFile file, String folderName) {
        log.info("开始上传文件: {}, addToWorkspaces: {}",
            file != null ? file.getOriginalFilename() : "null", folderName);

        try {
            // 构建表单参数
            Map<String, Object> formParams = new HashMap<>();

            String url = "/document/upload";
            if (StrUtil.isNotBlank(folderName)) {
                url += "/" + folderName;
            }

            // 调用LlmCommonApi的文件上传方法
            DocumentResultResponseVO response = llmCommonApi.postFileUpload(url, file, formParams, DocumentResultResponseVO.class);

            if (response != null) {
                log.info("文件上传成功: {}, 响应: success={}, documents count={}",
                    file.getOriginalFilename(), response.isSuccess(),
                    response.getDocuments() != null ? response.getDocuments().size() : 0);
                return Result.success(response);
            } else {
                log.error("文件上传失败: 响应为空");
                return Result.failure("上传失败: 服务器响应为空");
            }

        } catch (IllegalArgumentException e) {
            log.error("文件上传失败: 参数错误 - {}", e.getMessage());
            return Result.failure(e.getMessage());
        } catch (Exception e) {
            log.error("文件上传失败: 系统错误 - {}", e.getMessage(), e);
            return Result.failure("上传失败: " + e.getMessage());
        }
    }


    /**
     * 添加文件到数据库中
     * @param requestVO
     * @return
     */
    @Override
    public Result<String> addLlmWorkFile(LlmWorkFileRequestVO requestVO) {
        String parentCode = requestVO.getParentCode();
        String originalFileName = requestVO.getFileName();
        
        // 检查原始文件名是否已存在（同一工作区下）
        QueryWrapper<LlmWorkFileEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_code", parentCode);
        wrapper.eq("llm_file_name", originalFileName);
        wrapper.eq("workspace_slug", requestVO.getWorkspaceSlug());
        long count = llmWorkFileService.count(wrapper);
        
        String finalFileName = originalFileName;
        
        if (count > 0) {
            // 文件名已存在，需要生成带版本号的文件名
            finalFileName = generateVersionedFileName(parentCode, originalFileName, requestVO.getWorkspaceSlug());
        }
        

            // 创建新的文件实体
            LlmWorkFileEntity fileEntity = documentConvert.convertLlmWorkFileEntity(requestVO);

            fileEntity.setFileName(finalFileName);
            
            // 保存到数据库
           llmWorkFileService.save(fileEntity);
            
            return Result.success(finalFileName);
    }


    /**
     * 创建显示文件夹
     * @param requestVO
     * @return
     */
    @Override
    public Result<String> addFolder(LlmWorkFileRequestVO requestVO) {

        String parentCode = requestVO.getParentCode();
        String fileName = requestVO.getFileName();

        // 检查原始文件名是否已存在（同一工作区下）
        QueryWrapper<LlmWorkFileEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_code", parentCode);
        wrapper.eq("llm_file_name", fileName);
        wrapper.eq("workspace_slug", requestVO.getWorkspaceSlug());

        long count = llmWorkFileService.count(wrapper);

        if (count > 0) {
            // 文件夹名已存在，需要生成带版本号的文件夹名
            return Result.failure("创建文件夹失败，该名称已存在");
        }

        // 创建新的文件夹实体
        LlmWorkFileEntity folderEntity = documentConvert.convertFolderEntity(requestVO);

        // 保存到数据库
        llmWorkFileService.save(folderEntity);

        return Result.success(fileName);
    }


    /**
     * 删除文件
     * @param fileCode
     * @return
     */
    @Override
    public Result<String> deleteFile(String fileCode) {

        if (StrUtil.isBlank(fileCode)) {
            return Result.failure("文件编码不能为空");
        }

        QueryWrapper<LlmWorkFileEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("file_code", fileCode);
        LlmWorkFileEntity llmWorkFileEntity = llmWorkFileService.getOne(wrapper);

        //如果是文件夹，校验下面是否有文件
        if(llmWorkFileEntity.getWhetherFolder()){
            QueryWrapper<LlmWorkFileEntity> folderWrapper = new QueryWrapper<>();
            folderWrapper.eq("parent_code", fileCode);
            long count = llmWorkFileService.count(folderWrapper);
            if(count > 0){

                List<LlmWorkFileEntity> llmWorkFile = findLlmWorkFile(fileCode, llmWorkFileEntity.getWorkspaceSlug());
                QueryWrapper<LlmWorkFileEntity> deleteWrapper = new QueryWrapper<>();
                List<String> fileCodeList = llmWorkFile.stream().map(LlmWorkFileEntity::getFileCode).collect(Collectors.toList());
                wrapper.in("file_code", fileCodeList);
                llmWorkFileService.remove(deleteWrapper);
            }
        }

        llmWorkFileService.remove(wrapper);


        return Result.success("删除成功");
    }


    /**
     * 改变文件状态
     * @param fileCode
     * @param whetherEnable
     * @return
     */
    @Override
    public Result<String> changeFileStatus(String fileCode, Boolean whetherEnable) {

        if (StrUtil.isBlank(fileCode)) {
            return Result.failure("文件编码不能为空");
        }

        QueryWrapper<LlmWorkFileEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("file_code", fileCode);
        LlmWorkFileEntity llmWorkFileEntity = llmWorkFileService.getOne(wrapper);

        List<String> fileCodeList = new ArrayList<>();
        fileCodeList.add(fileCode);

        //如果是文件夹，校验下面是否有文件
        if(llmWorkFileEntity.getWhetherFolder()){
            QueryWrapper<LlmWorkFileEntity> folderWrapper = new QueryWrapper<>();
            folderWrapper.eq("parent_code", fileCode);
            long count = llmWorkFileService.count(folderWrapper);
            if(count > 0){

                List<LlmWorkFileEntity> llmWorkFile = findLlmWorkFile(fileCode, llmWorkFileEntity.getWorkspaceSlug());
                List<String> fileList = llmWorkFile.stream().map(LlmWorkFileEntity::getFileCode).collect(Collectors.toList());
                fileCodeList.addAll(fileList);
            }else{
                return Result.success("文件夹为空，没有可启用文件");
            }
        }

        wrapper = new QueryWrapper<>();
        wrapper.in("file_code", fileCodeList);
        List<LlmWorkFileEntity> fileEntityList = llmWorkFileService.list(wrapper);

        List<String> llmFileNameList = new ArrayList<>();
        for (LlmWorkFileEntity workFileEntity : fileEntityList) {
            workFileEntity.setWhetherEnable(whetherEnable == Boolean.TRUE ? 1 : 0);
            llmFileNameList.add(workFileEntity.getLlmFileName());
        }


        //修改llm文件嵌入
        UpdateWorkDocumentRequestVO requestVO = new UpdateWorkDocumentRequestVO();
        if (whetherEnable == Boolean.TRUE) {
            requestVO.getAdd().addAll(llmFileNameList);
        }else{
            requestVO.getDeletes().addAll(llmFileNameList);
        }
        Result<WorkspaceResponseVO> workspaceResponseVOResult = updateWorkDocument(llmWorkFileEntity.getWorkspaceSlug(), requestVO);
        if (workspaceResponseVOResult.isFailure()){
            return Result.failure("文件修改失败"+workspaceResponseVOResult.getMessage());
        }


        llmWorkFileService.updateBatchById(fileEntityList);

        return Result.success("修改成功");
    }


    /**
     * 根据文件编码查询文件
     * @param fileCode
     * @return
     */
    @Override
    public Result<LlmWorkFileResponseVO> findFileByCode(String fileCode) {

        if (StrUtil.isBlank(fileCode)) {
            return Result.failure("文件编码不能为空");
        }

        LlmWorkFileEntity llmWorkFileEntity = llmWorkFileService.getOne(new QueryWrapper<LlmWorkFileEntity>().eq("file_code", fileCode));


        return Result.success(documentConvert.convertLlmWorkFileResponseVO(llmWorkFileEntity));
    }




    /**
     * 获取该父类文件下的所有文件（递归查询多级目录）
     * @param parentCode
     * @param workspaceSlug
     * @return
     */
    private List<LlmWorkFileEntity> findLlmWorkFile(String parentCode, String workspaceSlug) {
        if (StrUtil.isBlank(parentCode)) {
            log.warn("父级代码为空，返回空列表");
            return new ArrayList<>();
        }
        
        List<LlmWorkFileEntity> allFiles = new ArrayList<>();
        
        // 查询当前层级的所有文件和文件夹
        QueryWrapper<LlmWorkFileEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_code", parentCode);
        wrapper.eq(StrUtil.isNotBlank(workspaceSlug), "workspace_slug", workspaceSlug);
        wrapper.orderByAsc("file_name");
        
        List<LlmWorkFileEntity> currentLevelItems = llmWorkFileService.list(wrapper);
        
        for (LlmWorkFileEntity item : currentLevelItems) {
            // 添加当前项到结果列表
            allFiles.add(item);
            
            // 如果是文件夹，递归查询子文件夹下的所有文件
            if (item.getWhetherFolder() != null && item.getWhetherFolder()) {
                List<LlmWorkFileEntity> subFiles = findLlmWorkFile(item.getFileCode(), workspaceSlug);
                allFiles.addAll(subFiles);
            }
        }

        
        return allFiles;
    }

    /**
     * 获取该父类文件下的所有文件（递归查询多级目录，仅返回文件，不包含文件夹）
     * @param parentCode
     * @return
     */
    private List<LlmWorkFileEntity> findLlmWorkFileOnly(String parentCode, String workspaceSlug) {
        List<LlmWorkFileEntity> allItems = findLlmWorkFile(parentCode, workspaceSlug);
        
        // 过滤出只有文件的列表
        List<LlmWorkFileEntity> filesOnly = allItems.stream()
                .filter(item -> item.getWhetherFolder() == null || !item.getWhetherFolder())
                .collect(Collectors.toList());

        
        return filesOnly;
    }

    /**
     * 生成带版本号的文件名
     * @param parentCode 父级代码
     * @param originalFileName 原始文件名
     * @param workspaceSlug 工作区slug
     * @return 带版本号的文件名
     */
    private String generateVersionedFileName(String parentCode, String originalFileName, String workspaceSlug) {
        String fileNameWithoutExt;
        String fileExtension = "";
        
        // 分离文件名和扩展名
        int lastDotIndex = originalFileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < originalFileName.length() - 1) {
            fileNameWithoutExt = originalFileName.substring(0, lastDotIndex);
            fileExtension = originalFileName.substring(lastDotIndex);
        } else {
            fileNameWithoutExt = originalFileName;
        }
        
        int version = 1;
        String versionedFileName;
        
        // 循环查找可用的版本号
        while (true) {
            versionedFileName = fileNameWithoutExt + "(" + version + ")" + fileExtension;
            
            QueryWrapper<LlmWorkFileEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("parent_code", parentCode);
            wrapper.eq("llm_file_name", versionedFileName);
            wrapper.eq("workspace_slug", workspaceSlug);
            long count = llmWorkFileService.count(wrapper);
            
            if (count == 0) {
                break; // 找到可用的文件名
            }
            version++;
        }
        
        return versionedFileName;
    }


    /**
     * 创建文件夹
     * @param requestVO
     * @return
     */
    @Override
    public Result<FolderResponseVO> creatFolder(FolderRequestVO requestVO) {

        if (StrUtil.isBlank(requestVO.getName())) {
            return Result.failure("name不能为空");
        }



        FolderResponseVO folderResponseVO = llmCommonApi.postResult("/document/create-folder", requestVO, FolderResponseVO.class);

        return Result.success(folderResponseVO);
    }


    /**
     * 更新工作区文档
     * @param workSpaceSlug
     * @param requestVO
     * @return
     */
    @Override
    public Result<WorkspaceResponseVO> updateWorkDocument(String workSpaceSlug, UpdateWorkDocumentRequestVO requestVO) {

        if (StringUtils.isBlank(workSpaceSlug)) {
            return Result.failure("工作区slug不能为空");
        }

        WorkspaceResponseVO workspaceResponseVO= llmCommonApi.postResult("/workspace/"+workSpaceSlug+"/update-embeddings", requestVO, WorkspaceResponseVO.class);


        return Result.success(workspaceResponseVO);
    }



    /**
     * 删除文件夹下的文档
     * @param requestVO
     * @return
     */
    @Override
    public Result<FolderResponseVO> deleteFolder(FolderRequestVO requestVO) {


        if(StringUtils.isBlank(requestVO.getName())){
            return Result.failure("name不能为空");
        }

        FolderResponseVO folderResponseVO = llmCommonApi.postResult("/document/remove-folder", requestVO, FolderResponseVO.class);
        return Result.success(folderResponseVO);
    }


    /**
     * 查询文件列表
     * @param parentCode     父级编码
     * @param fileName       文件名称
     * @param whetherEnable  是否启用
     * @param fileSuffix     文件后缀
     * @return
     */
    @Override
    public Result<List<LlmWorkFileResponseVO>> findFileList(String parentCode,String fileName, Boolean whetherEnable, String fileSuffix, String workspaceSlug) {

        if (StrUtil.isBlank(parentCode)) {
            return Result.failure("父级文件编码不能为空");
        }


        QueryWrapper<LlmWorkFileEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_code", parentCode);
        wrapper.like(StrUtil.isNotBlank(fileName), "file_name", fileName);
        wrapper.eq(Objects.nonNull(whetherEnable), "whether_enable", whetherEnable);
        wrapper.eq(StrUtil.isNotBlank(workspaceSlug), "workspace_slug", workspaceSlug);
        wrapper.eq(StrUtil.isNotBlank(fileSuffix), "file_suffix", fileSuffix);

        List<LlmWorkFileEntity> llmWorkFileEntityList = llmWorkFileService.list(wrapper);

        List<LlmWorkFileResponseVO> llmWorkFileResponseVOS = documentConvert.convertLlmWorkFileResponseVOList(llmWorkFileEntityList);
        return Result.success(llmWorkFileResponseVOS);
    }

    /**
     * 修改文件信息
     * @param fileCode
     * @param newFileName
     * @return
     */
    @Override
    public Result<String> updateFile(String fileCode, String newFileName) {
        if (StrUtil.isBlank(fileCode)) {
            return Result.failure("文件编码不能为空");
        }
        
        if (StrUtil.isBlank(newFileName)) {
            return Result.failure("文件名不能为空");
        }
        
        // 查询要修改的文件
        QueryWrapper<LlmWorkFileEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("file_code", fileCode);
        LlmWorkFileEntity fileEntity = llmWorkFileService.getOne(wrapper);
        
        if (fileEntity == null) {
            return Result.failure("文件不存在");
        }
        
        // 检查同一工作区下同级目录是否有重名文件
        QueryWrapper<LlmWorkFileEntity> checkWrapper = new QueryWrapper<>();
        checkWrapper.eq("parent_code", fileEntity.getParentCode());
        checkWrapper.eq("workspace_slug", fileEntity.getWorkspaceSlug());
        checkWrapper.eq("file_name", newFileName);
        checkWrapper.ne("file_code", fileCode); // 排除自己
        
        long count = llmWorkFileService.count(checkWrapper);
        if (count > 0) {
            return Result.failure("该目录下已存在同名文件");
        }
        
        // 更新文件名
        fileEntity.setFileName(newFileName);
        llmWorkFileService.updateById(fileEntity);
        
        return Result.success("文件修改成功");
    }




}
