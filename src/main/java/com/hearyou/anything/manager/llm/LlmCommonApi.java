package com.hearyou.anything.manager.llm;
import com.hearyou.common.base.Result;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

public interface LlmCommonApi {
    <T> T getResult(String apiPath, Map<String, Object> queryParams, Class<T> responseType);

    <T> T getResult(String apiPath, Map<String, Object> queryParams, Class<T> responseType,Object... pathParams);

    <T> List<T> getResultList(String apiPath, Map<String, Object> queryParams, ParameterizedTypeReference<List<T>> typeRef);

    <T> List<T> getResultList(String apiPath, Map<String, Object> queryParams, ParameterizedTypeReference<List<T>> typeRef,Object... pathParams);

    <T> T postResult(String apiPath, Object requestBody, Class<T> responseType);

    <T> T postResult(String apiPath, Object requestBody, Class<T> responseType,Object... pathParams);

    <T> T deleteResult(String apiPath, Class<T> responseType);

    <T> T deleteResult(String apiPath, Class<T> responseType,Object... pathParams);

    <T> void postStreamResult(String apiPath, Object requestBody, Class<T> responseType, Consumer<T> chunkConsumer);

    <T> T postFileUpload(String apiPath, MultipartFile file, Map<String, Object> formParams, Class<T> responseType);
}
