package com.hearyou.anything.manager.llm.impl;

import cn.hutool.core.util.StrUtil;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.llm.LlmAdminApi;
import com.hearyou.anything.manager.llm.LlmCommonApi;
import com.hearyou.anything.manager.vo.request.llm.admin.LlmNewUserRequestVO;
import com.hearyou.anything.manager.vo.request.llm.admin.LlmUpdateUserRequestVO;
import com.hearyou.anything.manager.vo.request.llm.admin.WorkerBindUsersResetRequestVO;
import com.hearyou.anything.manager.vo.response.llm.LlmBaseResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmMultiUserResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmNewUserResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmUsersResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmWorkBindUsersResultResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Llm Admin Service实现类
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/3 16:30
 */
@Slf4j
@Component
public class LlmAdminApiImpl implements LlmAdminApi {
    private LlmCommonApi llmCommonApi;

    @Autowired
    public LlmAdminApiImpl(LlmCommonApi llmCommonApi) {
        this.llmCommonApi = llmCommonApi;
    }

    /**
     * 判断是否为多用户模式
     * @return
     */
    @Override
    public Result<Boolean> isMultiUserMode() {
        LlmMultiUserResponseVO responseVO = llmCommonApi.getResult("/admin/is-multi-user-mode", null, LlmMultiUserResponseVO.class);
        if(null == responseVO || !responseVO.getMultiUser()){
            return Result.success(Boolean.FALSE);
        }
        return Result.success(Boolean.TRUE);
    }

    /**
     * 获取用户列表
     * @return
     */
    @Override
    public Result<LlmUsersResponseVO> getUsers() {
        LlmUsersResponseVO llmUsersResponseVO = llmCommonApi.getResult("/admin/users", null,LlmUsersResponseVO.class);
        return Result.success(llmUsersResponseVO);
    }

    /**
     * 添加llm用户
     * @param user
     * @return
     */
    @Override
    public Result<LlmNewUserResultResponseVO> addUser(LlmNewUserRequestVO user) {
        if(null == user){
            return Result.failure("创建Llm用户失败! 用户信息不能为空!");
        }
        LlmNewUserResultResponseVO newUserResultResponseVO = llmCommonApi.postResult("/admin/users/new", user, LlmNewUserResultResponseVO.class);
        return Result.success(newUserResultResponseVO);
    }

    /**
     * 绑定用户集合到当前工作空间
     * @param workSlug  工作空间slug|sample-workspace
     * @param requestVO
     * @return
     */
    @Override
    public Result<LlmWorkBindUsersResultResponseVO> bindUserToWorkspace(String workSlug, WorkerBindUsersResetRequestVO requestVO) {
        if(StrUtil.isBlank(workSlug)){
            return Result.failure("绑定用户到工作空间失败! 工作空间slug不能为空!");
        }
        if(null == requestVO){
            return Result.failure("绑定用户到工作空间失败! 请求参数不能为空!");
        }
        LlmWorkBindUsersResultResponseVO workBindUsersResultResponseVO = llmCommonApi.postResult("/admin/workspaces/{workspaceSlug}/manage-users", requestVO, LlmWorkBindUsersResultResponseVO.class, workSlug);
        return Result.success(workBindUsersResultResponseVO);
    }

    /**
     * 根据用户id删除用户名
     *
     * @param userId
     * @return
     */
    @Override
    public Result<LlmBaseResultResponseVO> deleteUserByUserId(String userId) {
        if(StrUtil.isBlank(userId)){
            return Result.failure("LLM删除用户失败! 用户id不能为空!");
        }
        LlmBaseResultResponseVO resultResponseVO = llmCommonApi.deleteResult("/admin/users/{userId}",LlmBaseResultResponseVO.class,userId);
        return Result.success(resultResponseVO);
    }

    /**
     * 更新llm用户信息
     * @param userId llm用户ID|1
     * @param user
     * @return
     */
    @Override
    public Result<LlmNewUserResultResponseVO> updateUser(String userId, LlmUpdateUserRequestVO user) {
        if(null == user){
            return Result.failure("修改Llm用户信息失败! 用户修改信息不能为空!");
        }
        if(StrUtil.isBlank(userId)){
            return Result.failure("修改Llm用户信息失败! 用户id不能为空!");
        }

        LlmNewUserResultResponseVO resultResponseVO = llmCommonApi.postResult("/admin/users/{id}", user, LlmNewUserResultResponseVO.class,userId);
        return Result.success(resultResponseVO);
    }
}
