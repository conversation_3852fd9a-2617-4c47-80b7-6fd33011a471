package com.hearyou.anything.manager.llm;

import com.hearyou.anything.manager.vo.request.llm.document.LlmWorkFileRequestVO;
import com.hearyou.anything.manager.vo.request.llm.document.UpdateWorkDocumentRequestVO;
import com.hearyou.anything.manager.vo.response.document.*;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspaceResponseVO;
import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.vo.request.llm.document.FolderRequestVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface DocumentApi {


    /**
     * 列出本地存储的文件
     * @return
     */
    Result<LocalFilesResponseVO> findLocalFiles();


    /**
     * 根据name查找文件
     * @param name
     * @return
     */
    Result<FileSystemItemResponseVO> findFileByName(String name);


    /**
     * 上传文件
     * @param file
     * @param folderName
     * @return
     */
    Result<DocumentResultResponseVO> uploadFile(MultipartFile file, String folderName);


    /**
     * 添加文件到数据库中
     * @param requestVO
     * @return
     */
    Result<String> addLlmWorkFile(LlmWorkFileRequestVO requestVO);


    /**
     * 创建显示文件夹
     * @param requestVO
     * @return
     */
    Result<String> addFolder(LlmWorkFileRequestVO requestVO);


    /**
     * 删除文件
     * @param fileCode
     * @return
     */
    Result<String> deleteFile(String fileCode);


    /**
     * 改变文件状态
     * @param fileCode
     * @param whetherEnable
     * @return
     */
    Result<String> changeFileStatus(String fileCode,Boolean whetherEnable);


    /**
     * 根据文件编码查询文件
     * @param fileCode
     * @return
     */
    Result<LlmWorkFileResponseVO> findFileByCode(String fileCode);


    /**
     * 创建文件夹（llm）
     * @param requestVO
     * @return
     */
    Result<FolderResponseVO> creatFolder(FolderRequestVO requestVO);


    /**
     * 更新工作区文档
     * @param workSpaceSlug
     * @param requestVO
     * @return
     */
    Result<WorkspaceResponseVO> updateWorkDocument(String workSpaceSlug, UpdateWorkDocumentRequestVO requestVO);


    /**
     * 删除文件夹下的文档(llm)
     * @param requestVO
     * @return
     */
    Result<FolderResponseVO> deleteFolder(FolderRequestVO requestVO);


    /**
     * 查询文件列表
     * @param parentCode
     * @param fileName
     * @param whetherEnable
     * @param fileSuffix
     * @param workspaceSlug
     * @return
     */
    Result<List<LlmWorkFileResponseVO>> findFileList(String parentCode,String fileName,Boolean whetherEnable,String fileSuffix,String workspaceSlug);

    /**
     * 修改文件信息
     * @param fileCode
     * @param newFileName
     * @return
     */
    Result<String> updateFile(String fileCode, String newFileName);
}
