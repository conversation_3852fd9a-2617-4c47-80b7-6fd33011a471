package com.hearyou.anything.manager.llm;

import com.hearyou.anything.manager.vo.request.llm.work.AddWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.UpdateWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspaceResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResponseVO;
import com.hearyou.common.base.Result;

public interface LlmWorkspaceApi {

    /**
     * 获取当前所有的工作区信息
     * @return
     */
    Result<WorkspacesResponseVO> getWorkspaces();

    /**
     * 根据slug获取工作区信息
     * @param slug
     * @return
     */
    Result<WorkspaceResponseVO> getWorkspaceBySlug(String slug);

    /**
     * 新建工作区
     * @param requestDTO
     * @return
     */
    Result<WorkspacesResultResponseVO> addWorkspace(AddWorkspaceRequestVO requestDTO);


    /**
     * 删除工作区
     * @param slug
     * @return
     */
    Result<String> deleteWorkspace(String slug);


    /**
     * 更新工作区
     * @param requestVO
     * @return
     */
    Result<WorkspacesResultResponseVO> updateWorkspace(UpdateWorkspaceRequestVO requestVO,String slug);
}
