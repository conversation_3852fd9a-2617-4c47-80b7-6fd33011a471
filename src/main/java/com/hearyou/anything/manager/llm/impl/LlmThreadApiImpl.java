package com.hearyou.anything.manager.llm.impl;

import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.llm.LlmCommonApi;
import com.hearyou.anything.manager.llm.LlmThreadApi;
import com.hearyou.anything.manager.vo.request.llm.thread.ThreadRequestVO;
import com.hearyou.anything.manager.vo.request.llm.thread.UserQueryRequestVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatHistoryResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatStreamResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ChatTextResponseVO;
import com.hearyou.anything.manager.vo.response.llm.thread.ThreadResultResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * LLM线程服务
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/9 11:18
 */
@Slf4j
@Component
public class LlmThreadApiImpl implements LlmThreadApi {

    @Autowired
    private LlmCommonApi llmCommonApi;


    /**
     * 新增线程
     * @param requestVO
     * @param workSpaceSlug
     * @return
     */
    @Override
    public Result<ThreadResultResponseVO> addThread(ThreadRequestVO requestVO, String workSpaceSlug) {

        ThreadResultResponseVO threadResponse = llmCommonApi.postResult("/workspace/"+workSpaceSlug+"/thread/new", requestVO, ThreadResultResponseVO.class);

        return Result.success(threadResponse);

    }


    /**
     * 修改线程名称
     * @param requestVO
     * @param workSpaceSlug
     * @return
     */
    @Override
    public Result<ThreadResultResponseVO> updateThread(ThreadRequestVO requestVO, String workSpaceSlug) {

        ThreadResultResponseVO threadResponse = llmCommonApi.postResult("/workspace/"+workSpaceSlug+"/thread/"+requestVO.getSlug()+"/update", requestVO, ThreadResultResponseVO.class);
        return Result.success(threadResponse);
    }



    /**
     * 删除线程
     * @param workSpaceSlug
     * @param threadSlug
     * @return
     */
    @Override
    public Result<String> deleteThread(String workSpaceSlug, String threadSlug) {

        String message = llmCommonApi.deleteResult("/workspace/" + workSpaceSlug+"/thread/"+threadSlug, String.class);
        return Result.success(message);
    }


    /**
     * 获取线程聊天历史记录
     * @param workSpaceSlug
     * @param threadSlug
     * @return
     */
    @Override
    public Result<ChatHistoryResponseVO> findChatHistory(String workSpaceSlug, String threadSlug) {

        ChatHistoryResponseVO chatHistoryResponseVO =  llmCommonApi.getResult("/workspace/"+workSpaceSlug+"/thread/"+threadSlug+"/chats", null, ChatHistoryResponseVO.class);
        return Result.success(chatHistoryResponseVO);
    }


    /**
     * 在线程中发起对话
     * @param workSpaceSlug
     * @param threadSlug
     * @param requestVO
     * @return
     */
    @Override
    public Result<ChatTextResponseVO> chatThread(String workSpaceSlug, String threadSlug, UserQueryRequestVO requestVO) {

        ChatTextResponseVO chatTextResponseVO = llmCommonApi.postResult("/workspace/"+workSpaceSlug+"/thread/"+threadSlug+"/chat", requestVO, ChatTextResponseVO.class);

        return Result.success(chatTextResponseVO);
    }

    /**
     * 在线程中发起流式对话
     * @param workSpaceSlug
     * @param threadSlug
     * @param requestVO
     * @return
     */
    @Override
    public SseEmitter chatThreadStream(String workSpaceSlug, String threadSlug, UserQueryRequestVO requestVO) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 设置超时和错误处理
        emitter.onTimeout(() -> {
            log.warn("SSE连接超时: workspace={}, thread={}", workSpaceSlug, threadSlug);
            emitter.complete();
        });

        emitter.onError((throwable) -> {
            log.error("SSE连接出错: workspace={}, thread={}", workSpaceSlug, threadSlug, throwable);
            emitter.completeWithError(throwable);
        });

        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始流式对话: workspace={}, thread={}", workSpaceSlug, threadSlug);

                Consumer<ChatStreamResponseVO> chunkConsumer = chunk -> {
                    try {
                        // 发送SSE数据到客户端
                        emitter.send(SseEmitter.event().name("message").data(chunk));

                        // 如果是结束标志，关闭连接
                        if (chunk.getClose() != null && chunk.getClose()) {
                            emitter.complete();
                        }

                        // 如果有错误，关闭连接
                        if (chunk.getError() != null && chunk.getError()) {
                            emitter.completeWithError(new RuntimeException("流式对话出现错误"));
                        }

                    } catch (IOException e) {
                        log.error("发送SSE数据失败: workspace={}, thread={}", workSpaceSlug, threadSlug, e);
                        emitter.completeWithError(e);
                    }
                };

                llmCommonApi.postStreamResult("/workspace/"+workSpaceSlug+"/thread/"+threadSlug+"/stream-chat",
                    requestVO, ChatStreamResponseVO.class, chunkConsumer);

            } catch (Exception e) {
                log.error("流式对话失败: workspace={}, thread={}", workSpaceSlug, threadSlug, e);
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }


}
