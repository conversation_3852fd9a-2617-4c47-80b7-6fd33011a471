package com.hearyou.anything.manager.llm.impl;

import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.llm.LlmAuthorizationApi;
import com.hearyou.anything.manager.llm.LlmCommonApi;
import com.hearyou.anything.manager.vo.response.llm.auth.LlmAuthResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * LLM认证服务
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/3 11:18
 */
@Slf4j
@Component
public class LlmAuthorizationApiImpl implements LlmAuthorizationApi {

    private LlmCommonApi llmCommonApi;

    @Autowired
    public LlmAuthorizationApiImpl(LlmCommonApi llmCommonApi) {
        this.llmCommonApi = llmCommonApi;
    }

    /**
     * 验证当前令牌是否有效
     * @return
     */
    @Override
    public Result<Boolean> auth() {
        LlmAuthResponseVO responseVO = llmCommonApi.getResult("/auth",null,LlmAuthResponseVO.class);
        if(null == responseVO || !responseVO.getAuthenticated()){
            return Result.success(Boolean.FALSE);
        }
        return Result.success(Boolean.TRUE);
    }
}
