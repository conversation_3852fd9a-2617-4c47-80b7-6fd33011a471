package com.hearyou.anything.manager.llm.impl;

import com.hearyou.anything.manager.llm.LlmCommonApi;
import com.hearyou.anything.manager.llm.LlmWorkspaceApi;
import com.hearyou.anything.manager.vo.request.llm.work.AddWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.request.llm.work.UpdateWorkspaceRequestVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspaceResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.work.WorkspacesResponseVO;
import com.hearyou.common.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Llm 工作区Api 服务实现类
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/11 18:27
 */
@Component
public class LlmWorkspaceApiImpl implements LlmWorkspaceApi {
    private LlmCommonApi llmCommonApi;

    @Autowired
    public LlmWorkspaceApiImpl(LlmCommonApi llmCommonApi) {
        this.llmCommonApi = llmCommonApi;
    }

    /**
     * 获取当前所有的工作区信息
     * @return
     */
    @Override
    public Result<WorkspacesResponseVO> getWorkspaces() {
        WorkspacesResponseVO workspacesResponseVO = llmCommonApi.getResult("/workspaces", null, WorkspacesResponseVO.class);
        return Result.success(workspacesResponseVO);
    }


    /**
     * 根据slug获取工作区信息
     * @param slug
     * @return
     */
    @Override
    public Result<WorkspaceResponseVO> getWorkspaceBySlug(String slug) {
        WorkspaceResponseVO workspaceResponseVO = llmCommonApi.getResult("/workspace/" + slug, null, WorkspaceResponseVO.class);

        return Result.success(workspaceResponseVO);
    }


    /**
     * 新建工作区
     * @param requestDTO
     * @return
     */
    @Override
    public Result<WorkspacesResultResponseVO> addWorkspace(AddWorkspaceRequestVO requestDTO) {

        WorkspacesResultResponseVO workspacesResultResponseVO = llmCommonApi.postResult("/workspace/new", requestDTO, WorkspacesResultResponseVO.class);
        return Result.success(workspacesResultResponseVO);
    }


    /**
     * 删除工作区
     * @param slug
     * @return
     */
    @Override
    public Result<String> deleteWorkspace(String slug) {

        String message = llmCommonApi.deleteResult("/workspace/" + slug, String.class);
        return Result.success(message);
    }



    /**
     * 更新工作区
     * @param requestVO
     * @return
     */
    @Override
    public Result<WorkspacesResultResponseVO> updateWorkspace(UpdateWorkspaceRequestVO requestVO,String slug) {
        WorkspacesResultResponseVO workspacesResultResponseVO = llmCommonApi.postResult("/workspace/"+slug+"/update", requestVO, WorkspacesResultResponseVO.class);
        return Result.success(workspacesResultResponseVO);
    }
}
