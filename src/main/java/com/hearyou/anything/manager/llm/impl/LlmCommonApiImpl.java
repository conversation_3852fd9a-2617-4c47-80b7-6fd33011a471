package com.hearyou.anything.manager.llm.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hearyou.anything.manager.conf.expand.RestClientService;
import com.hearyou.anything.manager.llm.LlmCommonApi;
import com.hearyou.anything.manager.utils.SseStreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * llm通用的请求实现
 */
@Slf4j
@Component
public class LlmCommonApiImpl implements LlmCommonApi {
    @Value("${anything-llm.base-url:}")
    private String llmBaseUrl;

    @Value("${anything-llm.token:}")
    private String token;

    private RestClientService restClientService;
    private WebClient webClient;
    private ObjectMapper objectMapper;

    @Autowired
    public LlmCommonApiImpl(RestClientService restClientService, ObjectMapper objectMapper) {
        this.restClientService = restClientService;
        this.objectMapper = objectMapper;
        this.webClient = WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(50 * 1024 * 1024)) // 50MB for file uploads
            .build();
    }

    @Override
    public <T> T getResult(String apiPath, Map<String, Object> queryParams, Class<T> responseType) {
        Map<String, String> headers = new HashMap<>();
        addTokenAndAcceptHeader(headers);
        final String url = llmBaseUrl + apiPath;

        return restClientService.getWithHeaders(url, headers,queryParams, responseType);
    }

    @Override
    public <T> T getResult(String apiPath, Map<String, Object> queryParams, Class<T> responseType, Object... pathParams) {
        Map<String, String> headers = new HashMap<>();
        addTokenAndAcceptHeader(headers);
        final String url = llmBaseUrl + apiPath;

        return restClientService.getWithHeaders(url, headers, queryParams, responseType,pathParams);
    }

    @Override
    public <T> List<T> getResultList(String apiPath, Map<String, Object> queryParams, ParameterizedTypeReference<List<T>> typeRef) {
        Map<String, String> headers = new HashMap<>();
        addTokenAndAcceptHeader(headers);
        final String url = llmBaseUrl + apiPath;

        return restClientService.getListWithHeaders(url, headers,queryParams, typeRef);
    }

    @Override
    public <T> List<T> getResultList(String apiPath, Map<String, Object> queryParams, ParameterizedTypeReference<List<T>> typeRef, Object... pathParams) {
        Map<String, String> headers = new HashMap<>();
        addTokenAndAcceptHeader(headers);
        final String url = llmBaseUrl + apiPath;

        return restClientService.getListWithHeaders(url, headers, queryParams, typeRef, pathParams);
    }

    @Override
    public <T> T postResult(String apiPath, Object requestBody, Class<T> responseType) {
        Map<String, String> headers = new HashMap<>();
        addTokenAndAcceptHeader(headers);
        final String url = llmBaseUrl + apiPath;

        return restClientService.postWithHeaders(url, headers, requestBody, responseType);
    }

    @Override
    public <T> T postResult(String apiPath, Object requestBody, Class<T> responseType, Object... pathParams) {
        Map<String, String> headers = new HashMap<>();
        addTokenAndAcceptHeader(headers);
        final String url = llmBaseUrl + apiPath;

        return restClientService.postWithHeaders(url, headers, requestBody,responseType,pathParams);
    }

    @Override
    public <T> T deleteResult(String apiPath, Class<T> responseType) {
        Map<String, String> headers = new HashMap<>();
        addTokenAndAcceptHeader(headers);
        final String url = llmBaseUrl + apiPath;

        return restClientService.deleteWithHeaders(url, headers, responseType);
    }

    @Override
    public <T> T deleteResult(String apiPath, Class<T> responseType, Object... pathParams) {
        Map<String, String> headers = new HashMap<>();
        addTokenAndAcceptHeader(headers);
        final String url = llmBaseUrl + apiPath;

        return restClientService.deleteWithHeaders(url, headers, responseType,pathParams);
    }

    /**
     * 流式POST请求
     * @param apiPath API路径
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @param chunkConsumer 处理每个数据块的回调函数
     * @param <T> 响应类型
     */
    @Override
    public <T> void postStreamResult(String apiPath, Object requestBody, Class<T> responseType, Consumer<T> chunkConsumer) {
        final String url = llmBaseUrl + apiPath;

        // 调用SseStreamUtil工具类进行流式请求处理
        SseStreamUtil.postStreamResult(webClient, url, token, requestBody, responseType, chunkConsumer, objectMapper);
    }

    /**
     * 文件上传POST请求
     * @param apiPath API路径
     * @param multipartFile 要上传的MultipartFile文件
     * @param formParams 额外的表单参数
     * @param responseType 响应类型
     * @param <T> 响应类型
     * @return 响应结果
     */
    @Override
    public <T> T postFileUpload(String apiPath, MultipartFile multipartFile, Map<String, Object> formParams, Class<T> responseType) {
        final String url = llmBaseUrl + apiPath;

        log.info("开始文件上传请求: {}", url);
        log.debug("文件信息: 名称={}, 大小={} bytes",
            multipartFile != null ? multipartFile.getOriginalFilename() : "null",
            multipartFile != null ? multipartFile.getSize() : 0);
        log.debug("表单参数: {}", formParams);

        // 参数验证
        if (multipartFile == null) {
            log.error("文件上传失败: 文件对象为空");
            throw new IllegalArgumentException("文件不能为空");
        }

        if (multipartFile.isEmpty()) {
            log.error("文件上传失败: 文件为空 - {}", multipartFile.getOriginalFilename());
            throw new IllegalArgumentException("文件内容为空: " + multipartFile.getOriginalFilename());
        }

        // 检查文件大小限制 (50MB)
        long maxFileSize = 50 * 1024 * 1024; // 50MB
        if (multipartFile.getSize() > maxFileSize) {
            log.error("文件上传失败: 文件过大 - {} bytes, 最大允许: {} bytes", multipartFile.getSize(), maxFileSize);
            throw new IllegalArgumentException("文件过大，最大允许50MB");
        }

        // 将MultipartFile转换为临时File
        File tempFile = null;

        try {
            // 将MultipartFile转换为临时File
            String originalFilename = multipartFile.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                originalFilename = "upload_file";
            }

            // 创建临时文件
            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf('.');
            if (lastDotIndex > 0) {
                fileExtension = originalFilename.substring(lastDotIndex);
            }

            Path tempPath = Files.createTempFile("upload_", fileExtension);
            tempFile = tempPath.toFile();

            // 将MultipartFile内容复制到临时文件
            Files.copy(multipartFile.getInputStream(), tempPath, StandardCopyOption.REPLACE_EXISTING);

            log.debug("创建临时文件: {}", tempFile.getAbsolutePath());

            // 构建multipart/form-data请求体
            MultipartBodyBuilder builder = new MultipartBodyBuilder();

            // 添加文件部分
            FileSystemResource fileResource = new FileSystemResource(tempFile);
            builder.part("file", fileResource)
                .header(HttpHeaders.CONTENT_DISPOSITION, "form-data; name=\"file\"; filename=\"" + originalFilename + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM);

            // 添加额外的表单参数
            if (formParams != null && !formParams.isEmpty()) {
                for (Map.Entry<String, Object> entry : formParams.entrySet()) {
                    if (entry.getValue() != null) {
                        builder.part(entry.getKey(), entry.getValue().toString());
                        log.debug("添加表单参数: {} = {}", entry.getKey(), entry.getValue());
                    }
                }
            }

            log.info("发送文件上传请求到: {}", url);

            // 发送请求
            T response = webClient.post()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData(builder.build()))
                .retrieve()
                .bodyToMono(responseType)
                .timeout(Duration.ofMinutes(10)) // 10分钟超时，适合大文件上传
                .block();

            if (response != null) {
                log.info("文件上传成功: {}, 响应类型: {}", originalFilename, response.getClass().getSimpleName());
                return response;
            } else {
                log.error("文件上传失败: 响应为空");
                throw new RuntimeException("上传失败: 服务器响应为空");
            }

        } catch (WebClientResponseException e) {
            log.error("文件上传失败: HTTP错误 - 状态码: {}, 响应体: {}",
                e.getStatusCode(), e.getResponseBodyAsString(), e);
            throw new RuntimeException("上传失败: " + e.getMessage(), e);
        } catch (IOException e) {
            log.error("文件上传失败: IO错误 - {}", e.getMessage(), e);
            throw new RuntimeException("文件处理失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("文件上传失败: 系统错误 - {}", e.getMessage(), e);
            throw new RuntimeException("上传失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.deleteIfExists(tempFile.toPath());
                    log.debug("已删除临时文件: {}", tempFile.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", e.getMessage());
                }
            }
        }
    }

    private void addTokenAndAcceptHeader(Map<String, String> headers) {
        headers.put(HttpHeaders.AUTHORIZATION, "Bearer " + token);
        headers.put(HttpHeaders.ACCEPT,"application/json");
    }
}
