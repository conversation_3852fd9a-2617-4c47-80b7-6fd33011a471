package com.hearyou.anything.manager.llm;

import com.hearyou.common.base.Result;
import com.hearyou.anything.manager.vo.request.llm.admin.LlmNewUserRequestVO;
import com.hearyou.anything.manager.vo.request.llm.admin.LlmUpdateUserRequestVO;
import com.hearyou.anything.manager.vo.request.llm.admin.WorkerBindUsersResetRequestVO;
import com.hearyou.anything.manager.vo.response.llm.LlmBaseResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmNewUserResultResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmUsersResponseVO;
import com.hearyou.anything.manager.vo.response.llm.admin.LlmWorkBindUsersResultResponseVO;

public interface LlmAdminApi {
    /**
     * 判断是否为多用户模式
     * @return
     */
   Result<Boolean> isMultiUserMode();

    /**
     * 获取用户列表
     * @return
     */
   Result<LlmUsersResponseVO> getUsers();

    /**
     * 添加llm用户
     * @param user
     * @return
     */
   Result<LlmNewUserResultResponseVO> addUser(LlmNewUserRequestVO user);

    /**
     * 绑定用户集合到当前工作空间
     * @param workSlug 工作空间slug|sample-workspace
     * @param requestVO
     * @return
     */
   Result<LlmWorkBindUsersResultResponseVO> bindUserToWorkspace(String workSlug, WorkerBindUsersResetRequestVO requestVO);

    /**
     * 根据用户id删除用户名
     *
     * @param userId
     * @return
     */
    Result<LlmBaseResultResponseVO> deleteUserByUserId(String userId);

    /**
     * 更新llm用户信息
     * @param userId llm用户ID|1
     * @param user
     * @return
     */
    Result<LlmNewUserResultResponseVO> updateUser(String userId, LlmUpdateUserRequestVO user);

}
