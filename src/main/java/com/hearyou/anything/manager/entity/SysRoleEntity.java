package com.hearyou.anything.manager.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * 角色表表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-05-26
 */
@TableName("sys_role")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SysRoleEntity extends BaseEntity {
	
	
	/**
	 * 角色编码
	**/
	@TableField("role_code")
	private String roleCode;
	
	/**
	 * 角色名
	**/
	@TableField("role_name")
	private String roleName;
	
	/**
	 * 备注
	**/
	@TableField("remark")
	private String remark;
	
	/**
	 * 租户编码
	**/
	@TableField("tenant_code")
	private String tenantCode;
	
}
