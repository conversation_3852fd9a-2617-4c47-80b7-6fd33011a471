package com.hearyou.anything.manager.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * Llm用户和工作区绑定表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-06-11
 */
@TableName("llm_user_work")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LlmUserWorkEntity extends BaseEntity {
	
	
	/**
	 * 用户名
	**/
	@TableField("user_name")
	private String userName;
	
	/**
	 * 工作区slug
	**/
	@TableField("work_slug")
	private String workSlug;

}
