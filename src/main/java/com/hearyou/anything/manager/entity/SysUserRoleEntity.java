package com.hearyou.anything.manager.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * 用户角色表表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-05-26
 */
@TableName("sys_user_role")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SysUserRoleEntity extends BaseEntity {
	
	
	/**
	 * 关联编码
	**/
	@TableField("link_code")
	private String linkCode;
	
	/**
	 * 用户名
	**/
	@TableField("user_name")
	private String userName;
	
	/**
	 * 角色编码
	**/
	@TableField("role_code")
	private String roleCode;
	
	/**
	 * 租户编码
	**/
	@TableField("tenant_code")
	private String tenantCode;
	
}
