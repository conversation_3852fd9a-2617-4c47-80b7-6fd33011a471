package com.hearyou.anything.manager.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * 部门表表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-05-26
 */
@TableName("dept")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DeptEntity extends BaseEntity {
	
	
	/**
	 * 科室编码
	**/
	@TableField("dept_code")
	private String deptCode;
	
	/**
	 * 科室名称
	**/
	@TableField("dept_name")
	private String deptName;
	
	/**
	 * 上级科室编码
	**/
	@TableField("parent_code")
	private String parentCode;
	
	/**
	 * 租户编码
	**/
	@TableField("tenant_code")
	private String tenantCode;
	
}
