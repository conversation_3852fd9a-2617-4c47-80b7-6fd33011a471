package com.hearyou.anything.manager.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * LLM工作区线程表表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-07-28
 */
@TableName("llm_workspace_thread")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LlmWorkspaceThreadEntity extends BaseEntity {
	
	
	/**
	 * 工作区线程编码(系统业务编码)
	**/
	@TableField("workspace_thread_code")
	private String workspaceThreadCode;
	
	/**
	 * 工作区线程名称
	**/
	@TableField("workspace_thread_name")
	private String workspaceThreadName;
	
	/**
	 * 工作区线程slug
	**/
	@TableField("workspace_thread_slug")
	private String workspaceThreadSlug;
	
	/**
	 * 绑定的用户名;(需要转换成llm系统用户id)
	**/
	@TableField("user_name")
	private String userName;
	
	/**
	 * 工作区slug
	**/
	@TableField("workspace_slug")
	private String workspaceSlug;
	
	/**
	 * 是否启用;0-false 1-true
	**/
	@TableField("whether_enable")
	private Integer whetherEnable;
	
}
