package com.hearyou.anything.manager.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * 用户信息表表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-05-26
 */
@TableName("user_info")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UserInfoEntity extends BaseEntity {
	
	
	/**
	 * 用户名
	**/
	@TableField("user_name")
	private String userName;
	
	/**
	 * 密码
	**/
	@TableField("password")
	private String password;
	
	/**
	 * 姓名
	**/
	@TableField("name")
	private String name;
	
	/**
	 * 部门编码
	**/
	@TableField("dept_code")
	private String deptCode;
	
	/**
	 * 身份证号
	**/
	@TableField("id_card")
	private String idCard;
	
	/**
	 * 年龄
	**/
	@TableField("age")
	private Integer age;
	
	/**
	 * 手机号
	**/
	@TableField("phone_num")
	private String phoneNum;
	
	/**
	 * 住址
	**/
	@TableField("address")
	private String address;
	
	/**
	 * 性别;0-未知 1-男性 2-女性
	**/
	@TableField("sex")
	private Integer sex;
	
	/**
	 * 邮箱账号
	**/
	@TableField("email_account")
	private String emailAccount;
	
	/**
	 * 昵称
	**/
	@TableField("nick_name")
	private String nickName;
	
	/**
	 * 头像url
	**/
	@TableField("head_img")
	private String headImg;
	
	/**
	 * 注册账号时间
	**/
	@TableField("register_time")
	private Date registerTime;
	
	/**
	 * 注册时的IP
	**/
	@TableField("register_ip")
	private String registerIp;
	
	/**
	 * 注册审批人
	**/
	@TableField("register_approver")
	private String registerApprover;
	
	/**
	 * 上次登录时间
	**/
	@TableField("last_login_time")
	private Date lastLoginTime;

	/**
	 * llm系统用户id
	 */
	@TableField("llm_user_id")
	private String llmUserId;
	
	/**
	 * 用户名创建方式;0-管理员/系统创建 1-用户注册 2-外部系统导入 3-其他
	**/
	@TableField("user_create_type")
	private Integer userCreateType;
	
	/**
	 * 状态;0-激活 1-注册待审核 2-审核未通过 3-禁用 4-注销 5-其他
	**/
	@TableField("status")
	private Integer status;
	
	/**
	 * 租户编码
	**/
	@TableField("tenant_code")
	private String tenantCode;
	
}
