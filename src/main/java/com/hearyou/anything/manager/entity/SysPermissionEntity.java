package com.hearyou.anything.manager.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * 权限表表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-05-26
 */
@TableName("sys_permission")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SysPermissionEntity extends BaseEntity {
	
	
	/**
	 * 权限编码
	**/
	@TableField("permission_code")
	private String permissionCode;
	
	/**
	 * 权限名
	**/
	@TableField("permission_name")
	private String permissionName;
	
	/**
	 * 权限类型;0-菜单 1-按钮 2-接口API
	**/
	@TableField("permission_type")
	private Integer permissionType;
	
	/**
	 * 描述
	**/
	@TableField("remark")
	private String remark;
	
	/**
	 * 租户编码
	**/
	@TableField("tenant_code")
	private String tenantCode;
	
}
