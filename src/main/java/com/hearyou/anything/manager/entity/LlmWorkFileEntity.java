package com.hearyou.anything.manager.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * Llm工作区文件表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-07-17
 */
@TableName("llm_work_file")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LlmWorkFileEntity extends BaseEntity{
	
	
	/**
	 * 文件编码
	**/
	@TableField("file_code")
	private String fileCode;
	
	/**
	 * 业务类型
	**/
	@TableField("business_type")
	private String businessType;

	/**
	 * 文件后缀格式;(如.jpg，如果是文件夹则是/)
	 */
	@TableField("file_suffix")
	private String fileSuffix;

	/**
	 * 工作区slug
	 */
	@TableField("workspace_slug")
	private String workspaceSlug;
	
	/**
	 * 文件/文件夹名称;如果是文件，则为xxx.jpg，如果是文件夹则是xxx
	**/
	@TableField("file_name")
	private String fileName;
	
	/**
	 * LLM系统文件名;如果是文件类型，记录llm的文件名称（唯一标识）
	**/
	@TableField("llm_file_name")
	private String llmFileName;
	
	/**
	 * 上级编码
	**/
	@TableField("parent_code")
	private String parentCode;
	
	/**
	 * 文件大小(byte)
	**/
	@TableField("file_size")
	private Integer fileSize;
	
	/**
	 * 是否为文件夹;0-否 1-是
	**/
	@TableField("whether_folder")
	private Boolean whetherFolder;
	
	/**
	 * 文件md5签名
	**/
	@TableField("file_md5_sign")
	private String fileMd5Sign;
	
	/**
	 * 是否启用;0-false 1-true
	**/
	@TableField("whether_enable")
	private Integer whetherEnable;
	
}
