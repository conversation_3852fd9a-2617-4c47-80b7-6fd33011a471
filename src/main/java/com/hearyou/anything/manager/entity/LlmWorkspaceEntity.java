package com.hearyou.anything.manager.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * LLM系统工作区表表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-07-28
 */
@TableName("llm_workspace")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LlmWorkspaceEntity extends BaseEntity {
	
	
	/**
	 * 工作区编码
	**/
	@TableField("workspace_code")
	private String workspaceCode;
	
	/**
	 * 工作区名称
	**/
	@TableField("workspace_name")
	private String workspaceName;
	
	/**
	 * 工作区slug
	**/
	@TableField("workspace_slug")
	private String workspaceSlug;
	
	/**
	 * 是否启用;0-false 1-true
	**/
	@TableField("whether_enable")
	private Integer whetherEnable;
	
}
