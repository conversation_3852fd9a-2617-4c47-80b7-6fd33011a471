package com.hearyou.anything.manager.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.hearyou.common.base.BaseEntity;
import lombok.*;

/**
 * <p>
 * 角色权限表表实例
 * </p>
 * @作者: 潘娣超
 * @时间: 2025-05-26
 */
@TableName("sys_role_permission")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SysRolePermissionEntity extends BaseEntity {
	
	
	/**
	 * 关联编码
	**/
	@TableField("link_code")
	private String linkCode;
	
	/**
	 * 角色编码
	**/
	@TableField("role_code")
	private String roleCode;
	
	/**
	 * 权限编码
	**/
	@TableField("permission_code")
	private String permissionCode;
	
	/**
	 * 租户编码
	**/
	@TableField("tenant_code")
	private String tenantCode;
	
}
