package com.hearyou.anything.manager.conf;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 自定义id生成器
 */
@Slf4j
@Component
public class CustomIdGenerator implements IdentifierGenerator {

    @Autowired
    private Snowflake snowflake;

    @Override
    public Long nextId(Object entity) {
      	//可以将当前传入的class全类名来作为key,或者提取参数来生成bizKey进行分布式Id调用生成.
      	//final String className = entity.getClass().getName();
        long nextId = snowflake.nextId();
        log.info("id:{}",nextId);
        return nextId;
    }
}