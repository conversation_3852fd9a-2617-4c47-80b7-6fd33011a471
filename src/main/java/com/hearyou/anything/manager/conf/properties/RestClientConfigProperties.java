package com.hearyou.anything.manager.conf.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * RestClientConfigProperties配置属性类
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/27 15:05
 */
@Data
@Component
@ConfigurationProperties(prefix = RestClientConfigProperties.PREFIX)
public class RestClientConfigProperties {
    public static final String PREFIX = "rest-client";

    private Integer connectionRequestTimeout;
    private Integer responseTimeout;
    private Integer KeepAlive;
    private Boolean enableClientLog;

    // 连接池配置
    private Integer maxTotalConnections;
    private Integer defaultMaxPerRoute;

    // SSL配置
    private Boolean sslEnabled;
    /*private String keyStorePath;
    private String keyStorePassword;
    private String trustStorePath;
    private String trustStorePassword;*/

    public RestClientConfigProperties() {
        this.connectionRequestTimeout = 60;
        this.responseTimeout = 60;
        this.KeepAlive = 1800;
        this.enableClientLog = true;
        this.maxTotalConnections = 200;
        this.defaultMaxPerRoute = 20;
        this.sslEnabled = false;
    }
}
