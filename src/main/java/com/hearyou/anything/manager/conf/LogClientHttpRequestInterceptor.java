package com.hearyou.anything.manager.conf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 请求-响应 拦截器
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/27 17:26
 */
@Slf4j
public class LogClientHttpRequestInterceptor implements ClientHttpRequestInterceptor {
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        log.info("request method:{}, url: {}, headers: {}", request.getMethod(), request.getURI(), request.getHeaders());
        if (request.getMethod().equals(HttpMethod.POST)) {
            log.info("request body: {}", new String(body, StandardCharsets.UTF_8));
        }

        ClientHttpResponse response = execution.execute(request, body);
        ClientHttpResponse responseWrapper = new BufferingClientHttpResponseWrapper(response);
        String responseBody = StreamUtils.copyToString(responseWrapper.getBody(), StandardCharsets.UTF_8);
        log.info("response status:{},response body: {}", responseWrapper.getStatusCode(),responseBody);
        return responseWrapper;
    }
}
