package com.hearyou.anything.manager.conf;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: 潘娣超
 * @Date: 2021/5/25 : 10:11
 */
@Configuration
public class IdConfig {

    /**
     * id生成
    **/
    @Bean
    public Snowflake snowflake(){
        return IdUtil.getSnowflake(1,1);
    }
}
