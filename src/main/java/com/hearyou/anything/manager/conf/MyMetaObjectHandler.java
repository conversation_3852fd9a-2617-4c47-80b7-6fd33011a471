package com.hearyou.anything.manager.conf;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.Objects;

/**
 * 公共字段操作填充 例如insert的时候，update的时候，目前暂时放着，不确定新增和修改的时候会使用什么字段，建议是拿外部接口传入的
 * <AUTHOR>
 * @Date 2020/4/3010:35
 **/
@Component
@Slf4j
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Autowired
    private Snowflake snowflake;

    @Override
    public void insertFill(MetaObject metaObject) {
        if(log.isInfoEnabled()){
            log.info("insertFill");
        }

        // 做个兜底的操作，如果id存在且为空，则给一个默认的雪花ID，id有值的话，则不做任何操作
        if (metaObject.hasGetter("id") && (Objects.isNull(metaObject.getValue("id")) || "".equals(metaObject.getValue("id")))){
           //新增的时候，id生成并插入
           /*  setFieldValByName("id", metaObject.getValue("id"),metaObject);
        }else {*/
            //新增的时候，id生成并插入
            setFieldValByName("id",snowflake.nextId(),metaObject);
        }

        //这里进行线程上下文获取
        /*ContextInfoBO userContextInfo = UserThreadContext.getUserContextInfo();
        //如果userContextInfo不存在，则插入默认值(新增)
        setValueByFieldName(userContextInfo,metaObject,"");

        String userName = "";
        if(null != userContextInfo) {
            userName = userContextInfo.getUserName();
        }

        Object createBy = getFieldValByName("createBy", metaObject);
        if(Objects.isNull(createBy) || "".equals(createBy)){
            setFieldValByName("createBy", userName, metaObject);
        }

        Object updateBy = getFieldValByName("updateBy", metaObject);
        if(Objects.isNull(updateBy) || "".equals(updateBy)){
            setFieldValByName("updateBy", "", metaObject);
        }*/

        //获取当前的时间设置为创建时间
        Date createDate = new Date();
        setFieldValByName("createTime",createDate, metaObject);
        setFieldValByName("updateTime",createDate, metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if(log.isInfoEnabled()){
            log.info("updateFill");
        }

        /*ContextInfoBO userContextInfo = UserThreadContext.getUserContextInfo();
        setValueByFieldName(userContextInfo,metaObject,null);

        String userName = "";
        if(null != userContextInfo) {
            userName = userContextInfo.getUserName();
        }

        Object updateBy = getFieldValByName("updateBy", metaObject);
        if(Objects.isNull(updateBy) || "".equals(updateBy)){
            setFieldValByName("updateBy", userName, metaObject);
        }*/
        //获取当前的时间设置为创建时间
        Date updateDate = new Date();
        setFieldValByName("updateTime", updateDate, metaObject);
    }

    /**
     * 新增 + 修改的时候，都去同步修改一下表中的集团 + 公司信息
     **/
    /*private void setValueByFieldName(final ContextInfoBO userContextInfo,MetaObject metaObject,Object defaultValue) {
        Object blocCodeValue = defaultValue;
        Object companyCodeValue = defaultValue;
        if(Objects.nonNull(userContextInfo)){
            blocCodeValue = userContextInfo.getBlocCode();
            companyCodeValue = userContextInfo.getCompanyCode();
        }

        //判断原来是否有值，有值的话不允许插入进行替换
        Object blocCode = getFieldValByName("blocCode", metaObject);
        if(Objects.isNull(blocCode) || "".equals(blocCode)){
            setFieldValByName("blocCode", blocCodeValue, metaObject);
        }

        Object companyCode = getFieldValByName("companyCode", metaObject);
        if(Objects.isNull(companyCode) || "".equals(companyCode)){
            setFieldValByName("companyCode", companyCodeValue, metaObject);
        }*/

        //插入以后，判断是否为集团用户
        /*if(null != userContextInfo && userContextInfo.getWhetherBlocUser()){
            Object companyCode = getFieldValByName("companyCode", metaObject);
            if(null == blocCode){
                setFieldValByName("companyCode", companyCodeValue, metaObject);
            }
        }else{
            //否则插入默认值
            setFieldValByName("companyCode", defaultValue, metaObject);
        }*/

    /*}*/
}
