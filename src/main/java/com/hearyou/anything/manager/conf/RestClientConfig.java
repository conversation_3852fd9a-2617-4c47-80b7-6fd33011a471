package com.hearyou.anything.manager.conf;

import com.hearyou.anything.manager.conf.properties.RestClientConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClient;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class RestClientConfig {
    private RestClientConfigProperties restClientConfigProperties;

    public RestClientConfig(RestClientConfigProperties restClientConfigProperties) {
        this.restClientConfigProperties = restClientConfigProperties;
    }

    @Bean
    public RestClient restClient() {
        // 1. 创建连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(this.restClientConfigProperties.getMaxTotalConnections());
        connectionManager.setDefaultMaxPerRoute(this.restClientConfigProperties.getDefaultMaxPerRoute());

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(this.restClientConfigProperties.getConnectionRequestTimeout(), TimeUnit.SECONDS) // 从连接池获取连接的超时
                .setResponseTimeout(this.restClientConfigProperties.getResponseTimeout(), TimeUnit.SECONDS)
                .setDefaultKeepAlive(this.restClientConfigProperties.getKeepAlive(), TimeUnit.SECONDS)
                /*.setRedirectsEnabled(true)
                .setMaxRedirects()*/
                .build();

        // 3. 添加SSL配置
            /*if (this.restClientConfigProperties.getSslEnabled()) {

            }*/

        HttpClientBuilder httpClientBuilder = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig);
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClientBuilder.build());

        RestClient.Builder builder = RestClient.builder().requestFactory(requestFactory);
        //判断 是否添加请求-响应日志拦截器
        if(this.restClientConfigProperties.getEnableClientLog()){
            builder.requestInterceptor(new LogClientHttpRequestInterceptor());
        }
        return builder.build();
    }

    private SSLContext sslContext() {
        // 创建信任所有证书的 TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
            public X509Certificate[] getAcceptedIssuers() { return null; }
            public void checkClientTrusted(X509Certificate[] certs, String authType) {}
            public void checkServerTrusted(X509Certificate[] certs, String authType) {}
        }};

        // 初始化 SSLContext
        SSLContext sslContext;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, null);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize SSLContext", e);
        }
        return sslContext;
    }
}