package com.hearyou.anything.manager.conf;

import com.hearyou.anything.manager.exception.CommonException;
import com.hearyou.common.base.BaseResultEnum;
import com.hearyou.common.base.Result;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.util.validation.ValidationException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import java.sql.SQLIntegrityConstraintViolationException;

/**
 * 默认的异常处理
 * <AUTHOR>
 */
@Order(0)
@ControllerAdvice
@ResponseBody
@Slf4j
public class MyGlobalExceptionHandler{

    private static final String URL_MAIL = "http://mail/";

    /*@Autowired
    private RestTemplate restTemplate;*/

    @Value("${spring.application.name:unknown}")
    private String applicationName;

    public MyGlobalExceptionHandler() {
    }

    @ExceptionHandler({Exception.class})
    public Result defaultErrorHandler(HttpServletRequest req, Exception e) throws Exception {
        Result result = new Result();
        result.setMessage(e.getMessage());
        if (e instanceof NoHandlerFoundException) {
            result.setCode(404);
        } else if (e instanceof CommonException) {
            result.setCode(((CommonException) e).getCode());
            result.setMessage(e.getMessage());
        } else {
            result.setCode(500);
        }

        result.setData((Object) null);
        if (!(e instanceof NoHandlerFoundException) && !(e instanceof MissingServletRequestParameterException)) {
            /*this.sendEmail(req, e);*/
            if(log.isErrorEnabled()){
                log.error("error:{}",e);
            }
        }

        return result;
    }

    @ExceptionHandler({NullPointerException.class})
    public Object nullExceptionHandler(HttpServletRequest req, NullPointerException e) {
        if(log.isErrorEnabled()){
            log.error("NullPointerException:{}",e);
        }
        return Result.failure(BaseResultEnum.NULL_EXCEPTION.getCode(), BaseResultEnum.NULL_EXCEPTION.getMessage());
    }

    /**
     * 数据库插入转换异常
    **/
    @ExceptionHandler({SQLIntegrityConstraintViolationException.class})
    public Object sqlIntegrityConstraintViolationExceptionHandler(HttpServletRequest request,SQLIntegrityConstraintViolationException e){
        if(log.isErrorEnabled()){
            log.error("SQLIntegrityConstraintViolationException:{}",e);
        }
        return Result.failure(BaseResultEnum.SQL_EXCEPTION.getCode(), e.getMessage());
    }

    /**
     * 方法参数校验 --继承的类实现了，不能重复实现
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException exception) {
        Result result = new Result();
        result.setCode(BaseResultEnum.METHOD_ARGUMENT_NOT_VALID.getCode());
        StringBuilder builder = new StringBuilder();
        for (FieldError error : exception.getBindingResult().getFieldErrors()) {
            if(builder.length() > 0){
                builder.append("/");
            }
            builder.append("参数{").append(error.getField()).append("}").append(error.getDefaultMessage());
        }

        result.setMessage(builder.toString());
        return result;
    }

    /**
     * ValidationException
     */
    @ExceptionHandler({ValidationException.class})
    public Object handleValidationException(ValidationException e) {
        return new Result<>(BaseResultEnum.SYS_FAILURE.getCode(), e.toString(), null);
    }

    /**
     * ConstraintViolationException
     */
    /*@ExceptionHandler({ConstraintViolationException.class})
    public Object handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        StringBuilder builder = new StringBuilder();
        for (ConstraintViolation<?> constraintViolation : constraintViolations) {
            PathImpl pathImpl = (PathImpl) constraintViolation.getPropertyPath();
            // 读取参数字段，constraintViolation.getMessage() 读取验证注解中的message值
            String paramName = pathImpl.getLeafNode().getName();
            if(builder.length() > 0){
                builder.append("/");
            }
            builder.append("参数{").append(paramName).append("}").append(constraintViolation.getMessage());
        }
        return new Result<>(BaseResultEnum.PARAM_INVALID.getCode(), builder.toString(), null);
    }*/

    @ExceptionHandler({CommonException.class})
    public Object commonExceptionHandler(HttpServletRequest req, CommonException e) {
        return new Result<>(BaseResultEnum.SYS_FAILURE.getCode(), e.getMessage(), null);
    }

    @ExceptionHandler({MissingServletRequestPartException.class})
    public Object missingExceptionHandler(HttpServletRequest req, MissingServletRequestPartException e) {
        return new Result<>(HttpStatus.UNAUTHORIZED.value(), "缺少认证信息",null);
    }
}
