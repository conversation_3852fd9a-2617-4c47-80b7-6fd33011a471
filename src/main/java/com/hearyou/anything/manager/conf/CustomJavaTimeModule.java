package com.hearyou.anything.manager.conf;

import com.fasterxml.jackson.core.json.PackageVersion;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 配置java8 time的序列化器和反序列化器
 */
public class CustomJavaTimeModule extends SimpleModule {

	public static final DateTimeFormatter LocalDateTimePattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	public static final DateTimeFormatter LocalDatePattern = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	public static final DateTimeFormatter LocalTimePattern = DateTimeFormatter.ofPattern("HH:mm:ss");

	public CustomJavaTimeModule() {
		super(PackageVersion.VERSION);
		this.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(LocalDateTimePattern));
		this.addSerializer(LocalDate.class, new LocalDateSerializer(LocalDatePattern));
		this.addSerializer(LocalTime.class, new LocalTimeSerializer(LocalTimePattern));

		this.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(LocalDateTimePattern));
		this.addDeserializer(LocalDate.class, new LocalDateDeserializer(LocalDatePattern));
		this.addDeserializer(LocalTime.class, new LocalTimeDeserializer(LocalTimePattern));
	}
}