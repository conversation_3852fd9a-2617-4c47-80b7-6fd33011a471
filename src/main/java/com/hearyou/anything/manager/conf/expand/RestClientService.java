package com.hearyou.anything.manager.conf.expand;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import java.io.File;
import java.util.List;
import java.util.Map;

public interface RestClientService {

    //---------------- 基础操作 ----------------
    <T> T get(String uri, Class<T> responseType);

    <T> T get(String uri, Map<String, Object> queryParams, Class<T> responseType);

    <T> T getWithHeaders(String uri, Map<String, String> headers, 
                        Map<String, Object> queryParams, Class<T> responseType);

    <T> T get(String uri, Class<T> responseType,Object... uriVariables);

    <T> T get(String uri, Map<String, Object> queryParams, Class<T> responseType,Object... uriVariables);

    <T> T getWithHeaders(String uri, Map<String, String> headers,
                         Map<String, Object> queryParams, Class<T> responseType,Object... uriVariables);

    <T> T postWithHeaders(String uri, Object body, Class<T> responseType);

    <T> T postWithHeaders(String uri, Map<String, String> headers, Object body,
                          Class<T> responseType);

    <T> T postWithHeaders(String uri, Object body, Class<T> responseType,
                          Object... uriVariables);

    <T> T postWithHeaders(String uri, Map<String, String> headers, Object body,
                          Class<T> responseType, Object... uriVariables);

    <T> T put(String uri, Object body, Class<T> responseType);

    <T> T delete(String uri, Class<T> responseType);

    <T> T delete(String uri, Object body, Class<T> responseType,
               Object... uriVariables);

    <T> T deleteWithHeaders(String uri, Map<String, String> headers,
                            Class<T> responseType, Object... uriVariables);

    <T> T delete(String uri, Map<String, Object> queryParams,
                         Class<T> responseType,Object... uriVariables);

    <T> T deleteWithHeaders(String uri, Map<String, String> headers,
                         Map<String, Object> queryParams, Class<T> responseType,
                            Object... uriVariables);

    //---------------- 高级操作 ----------------
    <T> ResponseEntity<T> exchange(HttpMethod method, String uri,
                                   Object body,
                                   Class<T> responseType,
                                   Object... uriVariables);

    <T> List<T> getList(String uri, ParameterizedTypeReference<List<T>> typeRef);

    <T> List<T> getList(String uri, ParameterizedTypeReference<List<T>> typeRef,Object... uriVariables);

    <T> List<T> getList(String uri, Map<String, Object> queryParams,
                      ParameterizedTypeReference<List<T>> typeRef);

    <T> List<T> getList(String uri, Map<String, Object> queryParams,
                        ParameterizedTypeReference<List<T>> typeRef,Object... uriVariables);

    <T> List<T> getListWithHeaders(String uri, Map<String, String> headers,
                                   Map<String, Object> queryParams, ParameterizedTypeReference<List<T>> typeRef);

    <T> List<T> getListWithHeaders(String uri, Map<String, String> headers, Map<String, Object> queryParams,
                                   ParameterizedTypeReference<List<T>> typeRef,Object... uriVariables);

    String postForm(String uri, MultiValueMap<String, String> formData);

    Map<String, Object> uploadFile(String uri, File file, String fileName,
                                   Map<String, String> formFields);
}