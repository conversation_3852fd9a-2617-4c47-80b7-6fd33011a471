package com.hearyou.anything.manager.conf.expand;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;
import java.io.File;
import java.net.URI;
import java.util.List;
import java.util.Map;

@Component
public class RestClientImpl implements RestClientService {

    private RestClient restClient;

    @Autowired
    public RestClientImpl(RestClient restClient) {
        this.restClient = restClient;
    }

    //============= 基础方法实现 =============
    @Override
    public <T> T get(String uri, Class<T> responseType) {
        return restClient.get()
                .uri(uri)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T get(String uri, Map<String, Object> queryParams, 
                    Class<T> responseType) {
        URI expandedUri = buildUriWithParams(uri, queryParams);
        return restClient.get()
                .uri(expandedUri)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T getWithHeaders(String uri, Map<String, String> headers,
                               Map<String, Object> queryParams, 
                               Class<T> responseType) {
        URI expandedUri = buildUriWithParams(uri, queryParams);
        return restClient.get()
                .uri(expandedUri)
                .headers(h -> h.setAll(headers))
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T get(String uri, Class<T> responseType, Object... uriVariables) {
        return restClient.get()
                .uri(uri,uriVariables)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T get(String uri, Map<String, Object> queryParams, Class<T> responseType, Object... uriVariables) {
        URI expandedUri = buildUriWithParamsPathVariables(uri, queryParams,uriVariables);
        return restClient.get()
                .uri(expandedUri)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T getWithHeaders(String uri, Map<String, String> headers, Map<String, Object> queryParams, Class<T> responseType, Object... uriVariables) {
        URI expandedUri = buildUriWithParamsPathVariables(uri, queryParams,uriVariables);
        return restClient.get()
                .uri(expandedUri)
                .headers(h -> h.setAll(headers))
                .retrieve()
                .body(responseType);
    }

    //============= 带Body操作 =============
    @Override
    public <T> T postWithHeaders(String uri, Object body, Class<T> responseType) {
        return restClient.post()
                .uri(uri)
                .contentType(MediaType.APPLICATION_JSON)
                .body(body)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T postWithHeaders(String uri, Map<String, String> headers, Object body, Class<T> responseType) {
        return restClient.post()
                .uri(uri)
                .headers(h -> h.setAll(headers))  // 关键点：设置所有Header
                .contentType(MediaType.APPLICATION_JSON)
                .body(body)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T postWithHeaders(String uri, Object body, Class<T> responseType, Object... uriVariables) {
        return restClient.post()
                .uri(uri,uriVariables)
                .contentType(MediaType.APPLICATION_JSON)
                .body(body)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T postWithHeaders(String uri, Map<String, String> headers, Object body, Class<T> responseType, Object... uriVariables) {
        return restClient.post()
                .uri(uri,uriVariables)
                .headers(h -> h.setAll(headers))  // 关键点：设置所有Header
                .contentType(MediaType.APPLICATION_JSON)
                .body(body)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T put(String uri, Object body, Class<T> responseType) {
        return restClient.put()
                .uri(uri)
                .contentType(MediaType.APPLICATION_JSON)
                .body(body)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T delete(String uri, Class<T> responseType) {
        return restClient.delete()
                .uri(uri)  // 示例：传入 "/users/123"
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T delete(String uri, Object body, Class<T> responseType, Object... uriVariables) {
        return restClient.delete()
                .uri(uri,uriVariables)  // 示例：传入 "/users/123"
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T deleteWithHeaders(String uri, Map<String, String> headers, Class<T> responseType, Object... uriVariables) {
        return restClient.delete()
                .uri(uri,uriVariables)  // 示例：传入 "/users/123"
                .headers(h -> h.setAll(headers))
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T delete(String uri, Map<String, Object> queryParams, Class<T> responseType, Object... uriVariables) {
        URI expandedUri = buildUriWithParams(uri, queryParams);
        return restClient.delete()
                .uri(expandedUri)
                .retrieve()
                .body(responseType);
    }

    @Override
    public <T> T deleteWithHeaders(String uri, Map<String, String> headers, Map<String, Object> queryParams, Class<T> responseType, Object... uriVariables) {
        URI expandedUri = buildUriWithParams(uri, queryParams);
        return restClient.delete()
                .uri(expandedUri)  // 示例：传入 "/users/123"
                .headers(h -> h.setAll(headers))
                .retrieve()
                .body(responseType);
    }

    //============= 通用交换方法 =============
    @Override
    public <T> ResponseEntity<T> exchange(HttpMethod method, String uri,
                                          Object body,
                                          Class<T> responseType,
                                          Object... uriVariables) {
        return restClient.method(method)
                .uri(uri, uriVariables)
                .body(body)
                .retrieve()
                .toEntity(responseType);
    }

    //============= 集合返回处理 =============
    @Override
    public <T> List<T> getList(String uri,
                               ParameterizedTypeReference<List<T>> typeRef) {
        return restClient.get()
                .uri(uri)
                .retrieve()
                .body(typeRef);
    }

    @Override
    public <T> List<T> getList(String uri, ParameterizedTypeReference<List<T>> typeRef, Object... uriVariables) {
        return restClient.get()
                .uri(uri,uriVariables)
                .retrieve()
                .body(typeRef);
    }

    @Override
    public <T> List<T> getList(String uri, Map<String, Object> queryParams,
                             ParameterizedTypeReference<List<T>> typeRef) {
        URI expandedUri = buildUriWithParams(uri, queryParams);
        return restClient.get()
                .uri(expandedUri)
                .retrieve()
                .body(typeRef);
    }

    @Override
    public <T> List<T> getList(String uri, Map<String, Object> queryParams, ParameterizedTypeReference<List<T>> typeRef, Object... uriVariables) {
        URI expandedUri = buildUriWithParamsPathVariables(uri, queryParams,uriVariables);
        return restClient.get()
                .uri(expandedUri)
                .retrieve()
                .body(typeRef);
    }

    @Override
    public <T> List<T> getListWithHeaders(String uri, Map<String, String> headers, Map<String, Object> queryParams, ParameterizedTypeReference<List<T>> typeRef) {
        URI expandedUri = buildUriWithParams(uri, queryParams);
        return restClient.get()
                .uri(expandedUri)
                .headers(h -> h.setAll(headers))
                .retrieve()
                .body(typeRef);
    }

    @Override
    public <T> List<T> getListWithHeaders(String uri, Map<String, String> headers, Map<String, Object> queryParams, ParameterizedTypeReference<List<T>> typeRef, Object... uriVariables) {
        URI expandedUri = buildUriWithParamsPathVariables(uri, queryParams);
        return restClient.get()
                .uri(expandedUri)
                .headers(h -> h.setAll(headers))
                .retrieve()
                .body(typeRef);
    }

    //============= 表单与文件 =============
    @Override
    public String postForm(String uri, MultiValueMap<String, String> formData) {
        return restClient.post()
                .uri(uri)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(formData)
                .retrieve()
                .body(String.class);
    }

    @Override
    public Map<String, Object> uploadFile(String uri, File file, String fileName,
                                          Map<String, String> formFields) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new FileSystemResource(file));
        formFields.forEach((k, v) -> body.add(k, v));

        return restClient.post()
                .uri(uri)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(body)
                .retrieve()
                .body(new ParameterizedTypeReference<Map<String, Object>>() {});
    }

    //============= 工具方法 =============
    private URI buildUriWithParams(String uri, Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(uri);
        if(CollectionUtil.isNotEmpty(params)){
            params.forEach((key, value) -> {
                if (value instanceof Iterable) {
                    ((Iterable<?>) value).forEach(v -> builder.queryParam(key, v));
                } else if (value != null) {
                    builder.queryParam(key, value);
                }
            });
        }
        return builder.build().toUri();
    }

    private URI buildUriWithParamsPathVariables(String uri, Map<String, Object> params, Object... uriVariables) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(uri);
        builder.buildAndExpand(uriVariables);
        if(CollectionUtil.isNotEmpty(params)){
            params.forEach((key, value) -> {
                if (value instanceof Iterable) {
                    ((Iterable<?>) value).forEach(v -> builder.queryParam(key, v));
                } else if (value != null) {
                    builder.queryParam(key, value);
                }
            });
        }
        return builder.build().toUri();
    }

}