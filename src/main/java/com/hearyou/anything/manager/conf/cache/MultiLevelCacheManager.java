package com.hearyou.anything.manager.conf.cache;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.data.redis.cache.RedisCacheManager;
import java.util.Collection;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MultiLevelCacheManager implements CacheManager {

    private final CaffeineCacheManager caffeineCacheManager;
    private final RedisCacheManager redisCacheManager;
    private final ConcurrentMap<String, Cache> cacheMap = new ConcurrentHashMap<>();

    public MultiLevelCacheManager(CaffeineCacheManager caffeineCacheManager, RedisCacheManager redisCacheManager) {
        this.caffeineCacheManager = caffeineCacheManager;
        this.redisCacheManager = redisCacheManager;
    }

    @Override
    public Cache getCache(String name) {
        return cacheMap.computeIfAbsent(name,
                key -> new MultiLevelCache(
                        caffeineCacheManager.getCache(name),
                        redisCacheManager.getCache(name)
                ));
    }

    @Override
    public Collection<String> getCacheNames() {
        return Stream.concat(
                caffeineCacheManager.getCacheNames().stream(),
                redisCacheManager.getCacheNames().stream()
        ).collect(Collectors.toSet());
    }

    private class MultiLevelCache implements Cache {
        private final Cache level1Cache;
        private final Cache level2Cache;

        public MultiLevelCache(Cache level1Cache, Cache level2Cache) {
            this.level1Cache = level1Cache;
            this.level2Cache = level2Cache;
        }

        @Override
        public String getName() {
            String level1Name = "";
            if(null != level1Cache){
                level1Name = level1Cache.getName();
            }

            String level2Name = "";
            if(null != level2Cache){
                level2Name = level2Cache.getName();
            }
            return String.format("%s:%s",level1Name,level2Name);
        }

        @Override
        public Object getNativeCache() {
            return this;
        }

        @Override
        public ValueWrapper get(Object key) {
            ValueWrapper value = level1Cache.get(key);
            if (value == null) {
                value = level2Cache.get(key);
                if (value != null) {
                    level1Cache.put(key, value.get());
                }
            }
            return value;
        }

        @Override
        public <T> T get(Object key, Class<T> type) {
            T value = level1Cache.get(key, type);
            if (value == null) {
                value = level2Cache.get(key, type);
                if (value != null) {
                    level1Cache.put(key, value);
                }
            }
            return value;
        }

        @Override
        public <T> T get(Object key, Callable<T> valueLoader) {
            T value = level1Cache.get(key, valueLoader);
            if (value == null) {
                value = level2Cache.get(key, valueLoader);
                if (value != null) {
                    level1Cache.put(key, value);
                }
            }
            return value;
        }

        @Override
        public void put(Object key, Object value) {
            level1Cache.put(key, value);
            level2Cache.put(key, value);
        }

        @Override
        public void evict(Object key) {
            level1Cache.evict(key);
            level2Cache.evict(key);
        }

        @Override
        public void clear() {
            level1Cache.clear();
            level2Cache.clear();
        }
    }
}
