<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="30 seconds">
    <!-- 从application.properties读取环境变量 -->
    <springProperty scope="context" name="LOG_PATH" source="logging.file.path" defaultValue="logs"/>
    <springProperty scope="context" name="LOG_LEVEL" source="logging.level.root" defaultValue="INFO"/>
    <springProperty scope="context" name="MAX_HISTORY" source="logging.max-history" defaultValue="180"/>
    <springProperty scope="context" name="TOTAL_SIZE_CAP" source="logging.total-size-cap" defaultValue="10GB"/>

    <!-- 定义日志输出格式 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${LOG_LEVEL}</level>
        </filter>
    </appender>

    <!-- 按月分目录的滚动日志配置 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 生成logs/2025-05/app-2025-05-21.0.log.gz结构 -->
        <file>${LOG_PATH}/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/%d{yyyy-MM}/app-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 单文件最大500MB -->
            <maxFileSize>500MB</maxFileSize>
            <!-- 保留180天历史 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <!-- 总日志量上限10GB -->
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            <!-- 启动时清理过期日志 -->
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <!-- 可选：按级别过滤 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 多环境配置示例 -->
    <springProfile name="dev">
        <logger name="com.example" level="DEBUG"/>
        <root level="DEBUG">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="${LOG_LEVEL}">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>

    <!-- 系统错误日志单独输出 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/%d{yyyy-MM}/error-%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 添加错误日志监控 -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
</configuration>