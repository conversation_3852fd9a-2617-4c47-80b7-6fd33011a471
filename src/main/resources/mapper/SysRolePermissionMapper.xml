<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hearyou.anything.manager.mapper.SysRolePermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hearyou.anything.manager.entity.SysRolePermissionEntity">
		<id column="id" property="id" />
		<result column="link_code" property="linkCode" />
		<result column="role_code" property="roleCode" />
		<result column="permission_code" property="permissionCode" />
		<result column="version" property="version" />
		<result column="del_flag" property="delFlag" />
		<result column="create_by" property="createBy" />
		<result column="update_by" property="updateBy" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
		<result column="tenant_code" property="tenantCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,link_code,role_code,permission_code,version,del_flag,create_by,update_by,
        create_time,update_time,tenant_code
    </sql>

</mapper>
