<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hearyou.anything.manager.mapper.UserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hearyou.anything.manager.entity.UserInfoEntity">
		<id column="id" property="id" />
		<result column="user_name" property="userName" />
		<result column="password" property="password" />
		<result column="name" property="name" />
		<result column="dept_code" property="deptCode" />
		<result column="id_card" property="idCard" />
		<result column="age" property="age" />
		<result column="phone_num" property="phoneNum" />
		<result column="address" property="address" />
		<result column="sex" property="sex" />
		<result column="email_account" property="emailAccount" />
		<result column="nick_name" property="nickName" />
		<result column="head_img" property="headImg" />
		<result column="register_time" property="registerTime" />
		<result column="register_ip" property="registerIp" />
		<result column="register_approver" property="registerApprover" />
		<result column="last_login_time" property="lastLoginTime" />
		<result column="llm_user_id" property="llmUserId" />
		<result column="user_create_type" property="userCreateType" />
		<result column="status" property="status" />
		<result column="version" property="version" />
		<result column="del_flag" property="delFlag" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
		<result column="tenant_code" property="tenantCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,user_name,password,name,dept_code,id_card,age,phone_num,
        address,sex,email_account,nick_name,head_img,register_time,register_ip,register_approver,
        last_login_time,llm_user_id,user_create_type,status,version,del_flag,create_by,create_time,update_by,
        update_time,tenant_code
    </sql>

</mapper>
