<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hearyou.anything.manager.mapper.LlmWorkFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hearyou.anything.manager.entity.LlmWorkFileEntity">
		<id column="id" property="id" />
		<result column="file_code" property="fileCode" />
		<result column="business_type" property="businessType" />
		<result column="file_name" property="fileName" />
		<result column="llm_file_name" property="llmFileName" />
		<result column="parent_code" property="parentCode" />
		<result column="file_size" property="fileSize" />
		<result column="whether_folder" property="whetherFolder" />
		<result column="file_md5_sign" property="fileMd5Sign" />
		<result column="whether_enable" property="whetherEnable" />
		<result column="version" property="version" />
		<result column="del_flag" property="delFlag" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
		<result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,file_code,business_type,file_name,llm_file_name,parent_code,file_size,whether_folder,
        file_md5_sign,whether_enable,version,del_flag,create_by,create_time,update_time,update_by
    </sql>

</mapper>
