<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hearyou.anything.manager.mapper.LlmWorkspaceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hearyou.anything.manager.entity.LlmWorkspaceEntity">
		<id column="id" property="id" />
		<result column="workspace_code" property="workspaceCode" />
		<result column="workspace_name" property="workspaceName" />
		<result column="workspace_slug" property="workspaceSlug" />
		<result column="whether_enable" property="whetherEnable" />
		<result column="version" property="version" />
		<result column="del_flag" property="delFlag" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,workspace_code,workspace_name,workspace_slug,whether_enable,version,del_flag,create_by,
        create_time,update_by,update_time
    </sql>

</mapper>
