spring:
  application:
    name: anything-llm-api-manager
  profiles:
    active: mysql

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.hearyou.anything.manager.entity
  configuration:
    map-underscore-to-camel-case: true
    cacheEnabled: false
    logImpl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: true
    db-config:
      id-type: ASSIGN_ID
      table-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  config: classpath:logback-spring.xml
  file:
    path: logs/${spring.application.name}
  level:
    root: INFO
  logback:
    rollingpolicy:
      max-history: 31
      total-size-cap: 10GB
