{
  "serverUrl": "http://{{ip}}:{{port}}", //设置服务器地址,非必须
  "isStrict": false, //是否开启严格模式
  "allInOne": true,  //是否将文档合并到一个文件中，一般推荐为true
  "outPath": "src/main/resources/static/doc", //指定文档的输出路径
  "coverOld": true,  //是否覆盖旧的文件，主要用于mardown文件覆盖
  "packageFilters": "com.hearyou.anything.manager.controller.*",//controller包过滤，多个包用英文逗号隔开，2.2.2开始需要采用正则：com.test.controller.*
  "md5EncryptedHtmlName": true,//只有每个controller生成一个html文件是才使用
  "projectName": "Rest文档",//配置自己的项目名称
  "appToken": "b22394d556d04cb8ab3581bfcefee7b1", //torna项目中-对应模块的token
  "openUrl": "https://sf.hearyouscm.com/torna/api",//torna平台地址
  "showAuthor":true,//是否显示接口作者名称，默认是true,不想显示可关闭
  "requestFieldToUnderline": true, //自动将驼峰入参字段在文档中转为下划线格式,//@since 1.8.7 版本开始
  "responseFieldToUnderline": true,//自动将驼峰入参字段在文档中转为下划线格式,//@since 1.8.7 版本开始
  "inlineEnum":true,//设置为true会将枚举详情展示到参数表中，默认关闭，//@since 1.8.8版本开始
  "recursionLimit":7,//设置允许递归执行的次数用于避免栈溢出，默认是7，正常为3次以内，//@since 1.8.8版本开始
  "displayActualType":true,//配置true会在注释栏自动显示泛型的真实类型短类名，@since 1.9.6
  "ignoreRequestParams":[], //忽略请求参数对象，把不想生成文档的参数对象屏蔽掉，@since 1.9.2
  "dataDictionaries": [], //配置数据字典，没有需求可以不设置
  "errorCodeDictionaries": [],//错误码列表，没有需求可以不设置
  "revisionLogs": [],//设置文档变更记录，没有需求可以不设置
  "customResponseFields": [
    {
      "name": "code",
      "desc": "响应码",
      "ownerClassName": "com.hearyou.anything.manager.base.Result",
      "value": "0"
    },
    {
      "name": "message",
      "desc": "响应信息",
      "ownerClassName": "com.hearyou.anything.manager.base.Result",
      "value": "success"
    },
    {
      "name": "data",
      "desc": "响应数据",
      "ownerClassName": "com.hearyou.anything.manager.base.Result",
      "value": null
    }
  ],//自定义添加字段和注释，api-doc后期遇到同名字段则直接给相应字段加注释，非必须
  "requestHeaders": [],//设置请求头，没有需求可以不设置
  "apiObjectReplacements": [],// 自smart-doc 1.8.5开始你可以使用自定义类覆盖其他类做文档渲染，使用全类名
  "apiConstants": [],//从1.8.9开始配置自己的常量类，smart-doc在解析到常量时自动替换为具体的值,如：http://localhost:8080/testConstants/+ApiVersion.VERSION中的ApiVersion.VERSION会被替换
  "sourceCodePaths": []//设置代码路径，smart-doc默认会自动加载src/main/java, 没有需求可以不设置 1.0.0以后版本此配置不再生效
}